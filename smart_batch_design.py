#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能分批方案设计和测试
按3000字符限制，确保每批都是完整句子
"""

import os
import re
import time
import requests
import json

class SmartBatchTranslator:
    def __init__(self, api_key, max_chars=3000):
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
        self.max_chars = max_chars
    
    def smart_batch_sentences(self, sentences):
        """智能分批算法：确保每批不超过3000字符且都是完整句子"""
        print(f"开始智能分批，限制: {self.max_chars} 字符")
        
        batches = []
        current_batch = []
        current_chars = 0
        
        for i, sentence in enumerate(sentences):
            sentence_chars = len(sentence)
            
            # 计算加入这个句子后的总字符数（包括格式化）
            # 格式：序号. 句子\n
            formatted_chars = len(f"{len(current_batch)+1}. {sentence}\n")
            
            # 检查加入这个句子后是否超过限制
            if current_chars + formatted_chars > self.max_chars:
                # 超过限制，保存当前批次，开始新批次
                if current_batch:  # 确保不是空批次
                    batches.append({
                        'sentences': current_batch.copy(),
                        'total_chars': current_chars,
                        'sentence_count': len(current_batch)
                    })
                    print(f"  批次 {len(batches)}: {len(current_batch)} 句, {current_chars} 字符")
                
                # 开始新批次
                current_batch = [sentence]
                current_chars = len(f"1. {sentence}\n")
            else:
                # 不超过限制，添加到当前批次
                current_batch.append(sentence)
                current_chars += formatted_chars
        
        # 添加最后一个批次
        if current_batch:
            batches.append({
                'sentences': current_batch.copy(),
                'total_chars': current_chars,
                'sentence_count': len(current_batch)
            })
            print(f"  批次 {len(batches)}: {len(current_batch)} 句, {current_chars} 字符")
        
        print(f"智能分批完成: 总共 {len(batches)} 批")
        return batches
    
    def format_batch_for_translation(self, batch_sentences):
        """格式化批次用于翻译"""
        sentences_text = ""
        for i, sentence in enumerate(batch_sentences, 1):
            sentences_text += f"{i}. {sentence}\n"
        
        prompt = f"""请将以下{len(batch_sentences)}个英文句子翻译成中文，要求：
1. 翻译要自然流畅
2. 保持原文语气和情感
3. 严格按照序号返回翻译结果
4. 格式：序号. 中文翻译

英文原文：
{sentences_text}

中文翻译："""
        
        return prompt
    
    def translate_batch(self, batch_sentences, batch_num):
        """翻译单个批次"""
        print(f"\n翻译批次 {batch_num}: {len(batch_sentences)} 个句子")
        
        # 格式化prompt
        prompt = self.format_batch_for_translation(batch_sentences)
        prompt_chars = len(prompt)
        print(f"  Prompt长度: {prompt_chars} 字符")
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.2,
                "topK": 1,
                "topP": 1,
                "maxOutputTokens": 4096,
            }
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            start_time = time.time()
            
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers=headers,
                data=json.dumps(payload),
                timeout=120
            )
            
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text']
                    translations = self.parse_batch_result(content, len(batch_sentences))
                    
                    print(f"  ✅ 翻译成功! 耗时: {end_time - start_time:.2f}秒")
                    print(f"  翻译结果数: {len(translations)}")
                    
                    return translations
                else:
                    raise Exception("API返回格式错误")
            else:
                raise Exception(f"API错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 翻译失败: {e}")
            return None
    
    def parse_batch_result(self, content, expected_count):
        """解析批量翻译结果"""
        content = content.strip()
        
        # 按行分割
        lines = content.split('\n')
        translations = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 匹配格式：数字. 翻译内容
            match = re.match(r'^(\d+)\.\s*(.+)$', line)
            if match:
                index = int(match.group(1))
                translation = match.group(2).strip()
                translations.append((index, translation))
        
        # 按序号排序并提取翻译内容
        translations.sort(key=lambda x: x[0])
        result = [t[1] for t in translations]
        
        return result
    
    def smart_translate_all(self, sentences):
        """智能分批翻译所有句子"""
        print(f"智能分批翻译开始")
        print(f"总句子数: {len(sentences)}")
        print("=" * 60)
        
        # 智能分批
        batches = self.smart_batch_sentences(sentences)
        
        # 翻译每个批次
        all_translations = []
        success_batches = 0
        
        for i, batch_info in enumerate(batches, 1):
            batch_sentences = batch_info['sentences']
            
            translations = self.translate_batch(batch_sentences, i)
            
            if translations:
                all_translations.extend(translations)
                success_batches += 1
            else:
                # 翻译失败，保持原文
                all_translations.extend(batch_sentences)
            
            # 避免API限制
            if i < len(batches):
                time.sleep(1)
        
        print(f"\n" + "=" * 60)
        print(f"智能分批翻译完成:")
        print(f"  总批次数: {len(batches)}")
        print(f"  成功批次: {success_batches}")
        print(f"  翻译句子数: {len(all_translations)}")
        
        return all_translations, batches

def test_smart_batch_design():
    """测试智能分批设计"""
    print("智能分批方案设计测试")
    print("=" * 60)
    
    # API密钥
    api_key = "AIzaSyBrEmZW9wqmyil7xPsbK2fgMb7LjII8NlQ"
    
    # 初始化智能分批翻译器
    translator = SmartBatchTranslator(api_key, max_chars=3000)
    
    # 读取句子
    srt_file = "complete_english.en.srt"
    
    if not os.path.exists(srt_file):
        print(f"❌ 文件不存在: {srt_file}")
        return False
    
    # 提取句子
    with open(srt_file, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    subtitle_lines = []
    lines = srt_content.split('\n')
    
    for line in lines:
        line = line.strip()
        if (line.isdigit() or '-->' in line or not line):
            continue
        subtitle_lines.append(line)
    
    full_text = ' '.join(subtitle_lines)
    sentences = re.split(r'(?<=[.!?])\s+', full_text.strip())
    sentences = [s.strip() for s in sentences if s.strip()]
    
    print(f"总句子数: {len(sentences)}")
    
    # 计算总字符数
    total_chars = sum(len(s) for s in sentences)
    print(f"总字符数: {total_chars}")
    print(f"平均每句: {total_chars / len(sentences):.1f} 字符")
    
    # 执行智能分批翻译
    start_time = time.time()
    translations, batches = translator.smart_translate_all(sentences)
    end_time = time.time()
    
    total_time = end_time - start_time
    
    # 性能统计
    print(f"\n性能统计:")
    print(f"  总耗时: {total_time:.2f}秒")
    print(f"  平均每句: {total_time / len(sentences):.3f}秒")
    print(f"  翻译速度: {len(sentences) / total_time:.2f} 句/秒")
    
    # 批次详情
    print(f"\n批次详情:")
    for i, batch_info in enumerate(batches, 1):
        print(f"  批次{i}: {batch_info['sentence_count']}句, {batch_info['total_chars']}字符")
    
    # 保存结果
    output_file = "smart_batch_result.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"智能分批翻译结果\n")
        f.write(f"总句子数: {len(sentences)}\n")
        f.write(f"总批次数: {len(batches)}\n")
        f.write(f"总耗时: {total_time:.2f}秒\n")
        f.write(f"翻译速度: {len(sentences) / total_time:.2f} 句/秒\n\n")
        
        for i, (original, translated) in enumerate(zip(sentences, translations), 1):
            f.write(f"{i:2d}. {original}\n")
            f.write(f"    → {translated} ({len(translated)}字符)\n\n")
    
    print(f"\n结果已保存到: {output_file}")
    
    return True

if __name__ == "__main__":
    test_smart_batch_design()
