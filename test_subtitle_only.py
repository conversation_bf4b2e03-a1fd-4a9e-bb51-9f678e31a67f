#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单独测试字幕处理功能
测试流程：英文字幕下载 → VTT转SRT → 谷歌翻译 → 去重 → 字数限制
"""

import os
import re
import time
import subprocess
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def download_english_subtitle_test(video_url, output_dir="test_subtitles"):
    """
    测试下载英文字幕
    """
    print("=== 步骤1: 下载英文字幕 ===")
    
    # 创建测试目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 构建下载命令
    vtt_filename_template = f'test_video.%(ext)s'
    vtt_file_path = os.path.join(output_dir, vtt_filename_template)
    
    command = [
        'yt-dlp',
        '--write-auto-sub',
        '--skip-download',
        '--sub-lang', 'en',  # 英文字幕
        '--sub-format', 'vtt',
        '--socket-timeout', '60',
        '-o', vtt_file_path,
        video_url
    ]
    
    print(f"执行命令: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ 英文字幕下载失败: {result.stderr}")
            return None
        
        # 检查生成的VTT文件
        expected_vtt_path = vtt_file_path.replace('%(ext)s', 'en.vtt')
        if os.path.exists(expected_vtt_path):
            file_size = os.path.getsize(expected_vtt_path)
            print(f"✅ 英文字幕下载成功: {expected_vtt_path}")
            print(f"   文件大小: {file_size} 字节")
            
            # 显示前几行内容
            with open(expected_vtt_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:10]
            print("   前10行内容:")
            for i, line in enumerate(lines, 1):
                print(f"   {i:2d}: {line.rstrip()}")
            
            return expected_vtt_path
        else:
            print(f"❌ VTT文件未生成: {expected_vtt_path}")
            return None
            
    except Exception as e:
        print(f"❌ 下载过程出错: {e}")
        return None

def convert_vtt_to_srt_test(vtt_file_path, max_length=26):
    """
    测试VTT转SRT，包含去重和字数限制
    """
    print(f"\n=== 步骤2: VTT转SRT (最大字数: {max_length}) ===")
    
    if not os.path.exists(vtt_file_path):
        print(f"❌ VTT文件不存在: {vtt_file_path}")
        return None
    
    srt_file_path = vtt_file_path.replace('.en.vtt', '.en.srt')
    
    try:
        with open(vtt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        cleaned_lines = []
        block = []
        index_counter = 1
        last_line_text = None
        duplicate_count = 0

        print("开始处理VTT内容...")
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()

            # 跳过头部信息
            if (not line or 
                line.startswith("WEBVTT") or 
                line.startswith("Kind:") or 
                line.startswith("Language:")):
                continue

            # 如果这是时间戳行
            if '-->' in line:
                # 先把上一个block写入
                if block:
                    cleaned_lines.append(str(index_counter))
                    cleaned_lines.append(block[0])  # 时间戳
                    for c in block[1:]:
                        cleaned_lines.append(c)
                    cleaned_lines.append("")  # 添加空行
                    index_counter += 1
                    block = []

                # 修正vtt->srt时间戳格式
                line = line.replace('.', ',')  # 00:00:01.000 -> 00:00:01,000
                # 移除VTT特有的属性
                line = re.sub(r'\s*align:\w+|\s*position:\d+%', '', line).strip()
                block.append(line)
                
            else:
                # 字幕内容行
                text = re.sub(r'<[^>]+>', '', line)  # 去掉HTML标签
                if text and text != last_line_text:
                    # 检查字数限制并拆分
                    if len(text) > max_length:
                        print(f"   长字幕拆分: '{text}' (长度: {len(text)})")
                        # 拆分长字幕
                        split_texts = []
                        while len(text) > max_length:
                            split_texts.append(text[:max_length])
                            text = text[max_length:]
                        split_texts.append(text)
                        block.extend(split_texts)
                        print(f"   拆分结果: {split_texts}")
                    else:
                        block.append(text)
                    last_line_text = text
                elif text == last_line_text:
                    duplicate_count += 1
                    print(f"   去重: 跳过重复字幕 '{text}'")

        # 处理最后一个block
        if block:
            cleaned_lines.append(str(index_counter))
            cleaned_lines.append(block[0])
            for c in block[1:]:
                cleaned_lines.append(c)
            cleaned_lines.append("")

        # 写入SRT文件
        with open(srt_file_path, 'w', encoding='utf-8') as sf:
            for cl in cleaned_lines:
                sf.write(cl + '\n')

        print(f"✅ VTT转SRT完成: {srt_file_path}")
        print(f"   总字幕块数: {index_counter - 1}")
        print(f"   去重数量: {duplicate_count}")
        print(f"   文件大小: {os.path.getsize(srt_file_path)} 字节")
        
        # 验证SRT格式
        verify_srt_format(srt_file_path, max_length)
        
        return srt_file_path
        
    except Exception as e:
        print(f"❌ VTT转SRT出错: {e}")
        return None

def verify_srt_format(srt_file_path, max_length):
    """
    验证SRT格式是否正确
    """
    print(f"\n=== 验证SRT格式 ===")
    
    try:
        with open(srt_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按空行分割字幕块
        blocks = content.strip().split('\n\n')
        
        print(f"总字幕块数: {len(blocks)}")
        
        format_errors = 0
        length_errors = 0
        
        for i, block in enumerate(blocks[:5], 1):  # 检查前5个块
            lines = block.strip().split('\n')
            print(f"\n字幕块 {i}:")
            
            if len(lines) < 3:
                print(f"   ❌ 格式错误: 行数不足 ({len(lines)} < 3)")
                format_errors += 1
                continue
            
            # 检查序号
            if not lines[0].isdigit():
                print(f"   ❌ 序号错误: '{lines[0]}'")
                format_errors += 1
            else:
                print(f"   ✅ 序号: {lines[0]}")
            
            # 检查时间戳
            if '-->' not in lines[1]:
                print(f"   ❌ 时间戳错误: '{lines[1]}'")
                format_errors += 1
            else:
                print(f"   ✅ 时间戳: {lines[1]}")
            
            # 检查字幕内容和字数
            subtitle_text = '\n'.join(lines[2:])
            text_length = len(subtitle_text)
            print(f"   ✅ 内容: '{subtitle_text}' (长度: {text_length})")
            
            if text_length > max_length:
                print(f"   ⚠️  超长警告: {text_length} > {max_length}")
                length_errors += 1
        
        print(f"\n验证结果:")
        print(f"   格式错误: {format_errors}")
        print(f"   超长字幕: {length_errors}")
        
        if format_errors == 0 and length_errors == 0:
            print("   ✅ SRT格式完全正确！")
        
    except Exception as e:
        print(f"❌ 验证SRT格式时出错: {e}")

def translate_srt_test(english_srt_path, max_translate_blocks=5):
    """
    测试SRT翻译功能（只翻译前几个块进行测试）
    """
    print(f"\n=== 步骤3: 翻译SRT (测试前{max_translate_blocks}个块) ===")
    
    if not os.path.exists(english_srt_path):
        print(f"❌ 英文SRT文件不存在: {english_srt_path}")
        return None
    
    chinese_srt_path = english_srt_path.replace('.en.srt', '.zh-Hans.srt')
    
    try:
        with open(english_srt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        translated_lines = []
        block_count = 0
        
        for line in lines:
            line = line.rstrip('\n\r')
            
            # 保留序号、时间戳和空行
            if (line.strip().isdigit() or 
                '-->' in line or 
                line.strip() == ''):
                translated_lines.append(line)
            else:
                # 翻译字幕内容行
                if block_count < max_translate_blocks:
                    try:
                        clean_text = re.sub(r'<[^>]+>', '', line)
                        if clean_text.strip():
                            print(f"   翻译中: '{clean_text}'")
                            translated_text = translator.translate(clean_text)
                            translated_lines.append(translated_text)
                            print(f"   翻译结果: '{translated_text}'")
                            time.sleep(0.5)  # 避免API限制
                            block_count += 1
                        else:
                            translated_lines.append(line)
                    except Exception as e:
                        print(f"   ⚠️  翻译失败，保留原文: {line} (错误: {e})")
                        translated_lines.append(line)
                else:
                    # 超过测试数量，保留原文
                    translated_lines.append(line)

        # 保存翻译后的文件
        with open(chinese_srt_path, 'w', encoding='utf-8') as f:
            for line in translated_lines:
                f.write(line + '\n')

        print(f"✅ SRT翻译完成: {chinese_srt_path}")
        print(f"   翻译块数: {block_count}")
        print(f"   文件大小: {os.path.getsize(chinese_srt_path)} 字节")
        
        return chinese_srt_path
        
    except Exception as e:
        print(f"❌ SRT翻译出错: {e}")
        return None

def main():
    """主测试函数"""
    print("字幕处理单独测试")
    print("=" * 50)
    print("测试流程:")
    print("1. 下载英文字幕 (VTT)")
    print("2. 转换VTT为SRT (去重 + 字数限制)")
    print("3. 翻译SRT为中文 (测试前5个块)")
    print("4. 验证格式正确性")
    print("=" * 50)
    
    # 测试视频
    video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    print(f"测试视频: {video_url}")
    
    # 步骤1: 下载英文字幕
    vtt_path = download_english_subtitle_test(video_url)
    if not vtt_path:
        print("❌ 英文字幕下载失败，测试终止")
        return 1
    
    # 步骤2: VTT转SRT
    srt_path = convert_vtt_to_srt_test(vtt_path, max_length=26)
    if not srt_path:
        print("❌ VTT转SRT失败，测试终止")
        return 1
    
    # 步骤3: 翻译测试
    chinese_srt_path = translate_srt_test(srt_path, max_translate_blocks=5)
    if not chinese_srt_path:
        print("❌ SRT翻译失败")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 字幕处理测试完成！")
    print("生成的文件:")
    print(f"   英文VTT: {vtt_path}")
    print(f"   英文SRT: {srt_path}")
    print(f"   中文SRT: {chinese_srt_path}")
    print("\n请检查生成的文件确认格式正确性。")
    
    return 0

if __name__ == "__main__":
    exit(main())
