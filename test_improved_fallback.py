#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的降级策略
"""

import os
import sys

# 添加当前目录到路径
sys.path.insert(0, '.')

def test_improved_vtt_conversion():
    """测试改进的VTT转换功能"""
    print("=== 测试改进的VTT转换 ===")
    
    from subtitle_fallback_improved import convert_vtt_to_srt_improved
    
    vtt_file = "real_test.en.vtt"
    srt_file = "real_test_improved.en.srt"
    
    if not os.path.exists(vtt_file):
        print(f"❌ VTT文件不存在: {vtt_file}")
        return False
    
    print(f"输入文件: {vtt_file}")
    print(f"输出文件: {srt_file}")
    
    # 使用改进的转换函数
    success = convert_vtt_to_srt_improved(vtt_file, srt_file, max_length=26)
    
    if success:
        print("✅ 改进的VTT转换成功")
        
        # 检查结果
        if os.path.exists(srt_file):
            with open(srt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 按空行分割字幕块
            blocks = content.strip().split('\n\n')
            print(f"   生成字幕块数: {len(blocks)}")
            
            # 检查前5个块的格式
            format_errors = 0
            for i, block in enumerate(blocks[:5], 1):
                lines = block.strip().split('\n')
                print(f"\n字幕块 {i}:")
                
                if len(lines) < 3:
                    print(f"   ❌ 格式错误: 行数不足 ({len(lines)} < 3)")
                    format_errors += 1
                else:
                    print(f"   ✅ 序号: {lines[0]}")
                    print(f"   ✅ 时间戳: {lines[1]}")
                    for j, content_line in enumerate(lines[2:], 1):
                        print(f"   ✅ 内容{j}: '{content_line}' (长度: {len(content_line)})")
            
            print(f"\n格式检查结果: {format_errors} 个错误")
            return format_errors == 0
        else:
            print("❌ 输出文件未生成")
            return False
    else:
        print("❌ VTT转换失败")
        return False

def test_fallback_strategy():
    """测试完整的降级策略"""
    print("\n=== 测试完整降级策略 ===")
    
    # 模拟配置
    os.environ['VTT_HAN_FOLDER'] = 'test_fallback'
    os.environ['SRT_HAN_FOLDER'] = 'test_fallback'
    
    # 创建测试目录
    os.makedirs('test_fallback', exist_ok=True)
    
    from subtitle_fallback_improved import process_subtitle_with_fallback
    
    # 测试视频信息
    video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    channel_name = "TestChannel"
    video_title = "Test Video Fallback"
    
    print(f"测试视频: {video_url}")
    print(f"频道: {channel_name}")
    print(f"标题: {video_title}")
    
    # 执行降级策略
    success = process_subtitle_with_fallback(video_url, channel_name, video_title)
    
    if success:
        print("✅ 降级策略测试成功")
        
        # 检查生成的文件
        srt_file = f'test_fallback/【{channel_name}】{video_title}.zh-Hans.srt'
        txt_file = f'test_fallback/【{channel_name}】{video_title}.zh-Hans.txt'
        
        files_to_check = [
            ("中文SRT文件", srt_file),
            ("中文TXT文件", txt_file)
        ]
        
        for file_desc, file_path in files_to_check:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ {file_desc}: {file_path} (大小: {file_size} 字节)")
            else:
                print(f"❌ {file_desc}: 文件不存在 - {file_path}")
        
        return True
    else:
        print("❌ 降级策略测试失败")
        return False

def main():
    """主测试函数"""
    print("改进的降级策略测试")
    print("=" * 50)
    
    tests = [
        ("改进的VTT转换", test_improved_vtt_conversion),
        ("完整降级策略", test_fallback_strategy)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！改进的降级策略可以正常使用。")
        return 0
    else:
        print("⚠️  部分测试失败，需要进一步调试。")
        return 1

if __name__ == "__main__":
    exit(main())
