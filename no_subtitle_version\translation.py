import httpx
import time
from deep_translator import GoogleTranslator
from config import logger, TRANSLATE_API_URL, TRANSLATE_API_KEY
from utils import sanitize_filename

# 初始化谷歌翻译器
translator = GoogleTranslator(source='auto', target='zh-CN')

def google_translate(title):
    """使用谷歌翻译进行标题翻译"""
    try:
        logger.warning("尝试使用谷歌翻译...")
        translated = translator.translate(title)
        return sanitize_filename(translated)  # 返回清理后的标题
    except Exception as e:
        logger.error(f"谷歌翻译失败: {e}")
        return sanitize_filename(title)  # 返回原始标题作为最后的备选

def translate_title(title):
    """调用自定义翻译API翻译视频标题"""
    headers = {
        "Authorization": TRANSLATE_API_KEY,
        "Content-Type": "application/json"
    }
    data = {
        "model": "gpt-3.5-turbo",  # 使用 gpt-3.5-turbo 模型
        "messages": [{"role": "user", "content": f"请将以下塔罗占卜的标题翻译为中文,只返回翻译好的一个标题: {title}"}]
    }
    try:
        response = httpx.post(TRANSLATE_API_URL, headers=headers, json=data, timeout=10)
        response_data = response.json()
        print("API响应:", response_data)  # 打印完整响应内容

        # 检查响应结构是否包含 choices 字段
        if 'choices' in response_data and len(response_data['choices']) > 0:
            translated_title = response_data['choices'][0]['message']['content'].strip()
            return sanitize_filename(translated_title)
        else:
            print("API响应缺少预期字段 'choices'，返回原始标题")
            raise ValueError("API返回格式错误")  # 抛出异常，触发谷歌翻译
    except Exception as e:
        print(f"API翻译出错: {e}")
        raise  # 抛出异常，供调用方捕获并触发谷歌翻译

def translate_title_with_retry(title, retries=3, delay=10):
    """调用翻译API翻译视频标题，带重试机制，失败时使用谷歌翻译"""
    for attempt in range(retries):
        try:
            logger.warning(f"尝试使用自定义API翻译，第 {attempt + 1} 次...")
            return translate_title(title)  # 尝试调用自定义翻译 API
        except Exception as e:
            logger.error(f"自定义API翻译出错，第 {attempt + 1} 次，等待 {delay} 秒后重试... 错误: {e}")
            if attempt < retries - 1:
                time.sleep(delay)
            else:
                logger.warning("自定义API翻译失败，尝试使用谷歌翻译...")
                return google_translate(title)  # 调用谷歌翻译作为备用
