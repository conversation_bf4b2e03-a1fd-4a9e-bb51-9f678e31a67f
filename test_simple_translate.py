#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的翻译测试
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def simple_translate_test():
    """简单的翻译测试"""
    print("简单翻译测试")
    print("=" * 30)
    
    # 使用现有的英文SRT文件
    english_srt = "real_test_improved.en.srt"
    
    if not os.path.exists(english_srt):
        print(f"❌ 英文SRT文件不存在: {english_srt}")
        return False
    
    print(f"✅ 找到英文SRT文件: {english_srt}")
    
    # 读取前几行进行测试
    with open(english_srt, 'r', encoding='utf-8') as f:
        lines = f.readlines()[:20]  # 只读前20行
    
    print(f"读取了 {len(lines)} 行")
    
    # 找到字幕内容行并翻译
    translated_lines = []
    translate_count = 0
    
    for i, line in enumerate(lines, 1):
        line = line.strip()
        print(f"第{i}行: '{line}'")
        
        # 跳过序号、时间戳和空行
        if (line.isdigit() or 
            '-->' in line or 
            not line):
            translated_lines.append(line)
            print(f"   跳过: 序号/时间戳/空行")
        else:
            # 这是字幕内容，尝试翻译
            try:
                clean_text = re.sub(r'<[^>]+>', '', line)
                if clean_text.strip():
                    print(f"   原文: '{clean_text}'")
                    translated_text = translator.translate(clean_text)
                    translated_lines.append(translated_text)
                    print(f"   译文: '{translated_text}'")
                    translate_count += 1
                    time.sleep(0.5)  # 避免API限制
                else:
                    translated_lines.append(line)
                    print(f"   跳过: 空内容")
            except Exception as e:
                print(f"   ❌ 翻译失败: {e}")
                translated_lines.append(line)
    
    # 保存结果
    output_file = "simple_translate_test.zh-Hans.srt"
    with open(output_file, 'w', encoding='utf-8') as f:
        for line in translated_lines:
            f.write(line + '\n')
    
    print(f"\n✅ 翻译测试完成")
    print(f"   翻译行数: {translate_count}")
    print(f"   输出文件: {output_file}")
    
    # 显示结果
    if os.path.exists(output_file):
        with open(output_file, 'r', encoding='utf-8') as f:
            result_content = f.read()
        
        print(f"\n翻译结果:")
        print("-" * 40)
        print(result_content)
        print("-" * 40)
        
        # 检查中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', result_content)
        print(f"中文字符数: {len(chinese_chars)}")
        
        return True
    else:
        print("❌ 输出文件未生成")
        return False

if __name__ == "__main__":
    simple_translate_test()
