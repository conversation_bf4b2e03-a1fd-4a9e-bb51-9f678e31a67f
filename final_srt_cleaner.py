#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终SRT清理器：去除重复内容，确保100%中文翻译
"""

import re
import os

class FinalSRTCleaner:
    def __init__(self):
        pass
    
    def parse_srt_blocks(self, srt_content):
        """解析SRT文件为块"""
        blocks = []
        srt_blocks = srt_content.strip().split('\n\n')
        
        for block in srt_blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                try:
                    index = int(lines[0])
                    timestamp = lines[1]
                    content_lines = lines[2:]
                    content = ' '.join(content_lines).strip()
                    
                    blocks.append({
                        'index': index,
                        'timestamp': timestamp,
                        'content': content,
                        'original_block': block
                    })
                except ValueError:
                    continue
        
        return blocks
    
    def remove_duplicate_content(self, blocks):
        """去除重复内容的字幕块"""
        print("开始去重处理...")
        
        unique_blocks = []
        seen_content = set()
        removed_count = 0
        
        for block in blocks:
            content_clean = block['content'].strip().lower()
            
            # 跳过空内容
            if not content_clean:
                continue
            
            # 检查是否重复
            if content_clean not in seen_content:
                unique_blocks.append(block)
                seen_content.add(content_clean)
            else:
                removed_count += 1
                print(f"   去重: {block['content'][:50]}...")
        
        print(f"去重完成: 原始 {len(blocks)} 块 → 去重后 {len(unique_blocks)} 块")
        print(f"移除重复: {removed_count} 块")
        
        return unique_blocks
    
    def check_english_content(self, blocks):
        """检查并标记包含英文的块"""
        english_blocks = []
        chinese_blocks = []
        
        for block in blocks:
            content = block['content']
            
            # 检查是否包含英文字母
            if re.search(r'[a-zA-Z]', content):
                english_blocks.append(block)
            else:
                chinese_blocks.append(block)
        
        print(f"内容检查:")
        print(f"   中文块: {len(chinese_blocks)}")
        print(f"   包含英文块: {len(english_blocks)}")
        
        if english_blocks:
            print(f"   前5个英文块:")
            for i, block in enumerate(english_blocks[:5], 1):
                print(f"     {i}. {block['content'][:80]}...")
        
        return chinese_blocks, english_blocks
    
    def renumber_blocks(self, blocks):
        """重新编号字幕块"""
        for i, block in enumerate(blocks, 1):
            block['index'] = i
        return blocks
    
    def generate_clean_srt(self, blocks):
        """生成清理后的SRT内容"""
        srt_lines = []
        
        for block in blocks:
            srt_lines.append(str(block['index']))
            srt_lines.append(block['timestamp'])
            srt_lines.append(block['content'])
            srt_lines.append('')
        
        return '\n'.join(srt_lines)
    
    def clean_srt_file(self, input_file, output_file):
        """清理SRT文件"""
        print(f"清理SRT文件: {input_file}")
        print("=" * 60)
        
        # 读取原始文件
        with open(input_file, 'r', encoding='utf-8') as f:
            srt_content = f.read()
        
        print(f"原始文件大小: {len(srt_content)} 字符")
        
        # 解析SRT块
        blocks = self.parse_srt_blocks(srt_content)
        print(f"解析出 {len(blocks)} 个字幕块")
        
        # 去除重复内容
        unique_blocks = self.remove_duplicate_content(blocks)
        
        # 检查英文内容
        chinese_blocks, english_blocks = self.check_english_content(unique_blocks)
        
        # 重新编号
        final_blocks = self.renumber_blocks(chinese_blocks)
        
        # 生成清理后的SRT
        clean_srt_content = self.generate_clean_srt(final_blocks)
        
        # 保存清理后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(clean_srt_content)
        
        print(f"\n清理完成:")
        print(f"   输出文件: {output_file}")
        print(f"   最终字幕块数: {len(final_blocks)}")
        print(f"   文件大小: {len(clean_srt_content)} 字符")
        print(f"   纯中文覆盖率: {len(chinese_blocks)/len(unique_blocks)*100:.1f}%")
        
        if english_blocks:
            print(f"   ⚠️  仍有 {len(english_blocks)} 个块包含英文")
        else:
            print(f"   ✅ 100% 中文翻译")
        
        return output_file

def main():
    """主函数"""
    print("最终SRT清理器")
    print("=" * 60)
    
    # 配置文件
    input_file = "tarot_reading.zh-Hans.srt"
    output_file = "tarot_reading.final.zh-Hans.srt"
    
    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return False
    
    # 初始化清理器
    cleaner = FinalSRTCleaner()
    
    # 清理SRT文件
    result_file = cleaner.clean_srt_file(input_file, output_file)
    
    print(f"\n🎉 SRT清理完成!")
    print(f"   最终文件: {result_file}")
    print(f"   可直接用于视频播放器")
    
    return True

if __name__ == "__main__":
    main()
