#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复的翻译分配：正确将翻译内容分配到各个字幕块
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def parse_srt_blocks(srt_content):
    """解析SRT文件为字幕块"""
    blocks = []
    srt_blocks = srt_content.strip().split('\n\n')
    
    for block in srt_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            index = int(lines[0])
            timestamp = lines[1]
            content_lines = lines[2:]
            content = ' '.join(content_lines).strip()
            if content:
                blocks.append({
                    'index': index,
                    'timestamp': timestamp,
                    'content': content
                })
    
    return blocks

def group_by_complete_sentences(blocks):
    """按完整句子组合"""
    sentences = []
    i = 0
    
    while i < len(blocks):
        current_block = blocks[i]
        content = current_block['content'].strip()
        
        if not content:
            i += 1
            continue
            
        # 特殊标记单独处理
        if content in ['[Music]', '[Applause]', '[Laughter]']:
            sentences.append({
                'blocks': [current_block],
                'content': content
            })
            i += 1
            continue
        
        # 开始组合句子
        sentence_blocks = [current_block]
        sentence_content = content
        
        # 检查当前块是否以句号结尾
        if re.search(r'[.!?]$', content):
            sentences.append({
                'blocks': sentence_blocks,
                'content': sentence_content
            })
            i += 1
            continue
        
        # 向后查找句子结尾
        j = i + 1
        while j < len(blocks):
            next_block = blocks[j]
            next_content = next_block['content'].strip()
            
            if not next_content:
                j += 1
                continue
            
            sentence_blocks.append(next_block)
            sentence_content += ' ' + next_content
            
            if re.search(r'[.!?]$', next_content):
                sentences.append({
                    'blocks': sentence_blocks,
                    'content': sentence_content
                })
                i = j + 1
                break
            
            j += 1
        else:
            sentences.append({
                'blocks': sentence_blocks,
                'content': sentence_content
            })
            i = j
    
    return sentences

def distribute_translation_to_blocks_fixed(translated_content, original_blocks):
    """修复版本：将翻译内容正确分配到原有的字幕块中"""
    print(f"     分配翻译内容: '{translated_content}' 到 {len(original_blocks)} 个块")
    
    if len(original_blocks) == 1:
        # 只有一个块，直接返回
        result = [{
            'index': original_blocks[0]['index'],
            'timestamp': original_blocks[0]['timestamp'],
            'content': translated_content
        }]
        print(f"     单块分配: [{original_blocks[0]['index']}] '{translated_content}'")
        return result
    
    # 多个块，需要分配
    # 将翻译内容按字符数平均分配（而不是按词分配）
    total_chars = len(translated_content)
    chars_per_block = total_chars // len(original_blocks)
    remainder = total_chars % len(original_blocks)
    
    distributed_blocks = []
    char_index = 0
    
    for i, block in enumerate(original_blocks):
        # 计算当前块应该分配的字符数
        current_chars_count = chars_per_block + (1 if i < remainder else 0)
        
        # 确保不会超出总长度
        end_index = min(char_index + current_chars_count, total_chars)
        
        if char_index < total_chars:
            # 尝试在合适的位置断开（避免在词中间断开）
            if end_index < total_chars:
                # 向后查找空格或标点符号
                for j in range(end_index, min(end_index + 10, total_chars)):
                    if translated_content[j] in ' ，。！？':
                        end_index = j + 1
                        break
                else:
                    # 向前查找空格或标点符号
                    for j in range(end_index - 1, max(char_index, end_index - 10), -1):
                        if translated_content[j] in ' ，。！？':
                            end_index = j + 1
                            break
            
            block_content = translated_content[char_index:end_index].strip()
            
            # 如果内容为空，给一个省略号
            if not block_content and i > 0:
                block_content = '...'
        else:
            block_content = '...'
        
        distributed_blocks.append({
            'index': block['index'],
            'timestamp': block['timestamp'],
            'content': block_content
        })
        
        print(f"     块 [{block['index']}]: '{block_content}'")
        char_index = end_index
    
    return distributed_blocks

def translate_and_distribute_fixed(sentences):
    """修复版本：翻译句子并正确分配到原有字幕块"""
    result_blocks = []
    
    for i, sentence in enumerate(sentences, 1):
        content = sentence['content'].strip()
        blocks = sentence['blocks']
        
        print(f"句子 {i}: '{content[:50]}{'...' if len(content) > 50 else ''}'")
        print(f"   包含 {len(blocks)} 个块: {[b['index'] for b in blocks]}")
        
        # 检查是否需要翻译
        has_punctuation = bool(re.search(r'[.!?,:;]', content))
        
        if not has_punctuation and content not in ['[Music]', '[Applause]', '[Laughter]']:
            print(f"   ⚠️  无标点符号，跳过翻译")
            for block in blocks:
                result_blocks.append({
                    'index': block['index'],
                    'timestamp': block['timestamp'],
                    'content': block['content']
                })
            continue
        
        # 翻译完整句子
        try:
            if content in ['[Music]', '[Applause]', '[Laughter]']:
                translation_map = {
                    '[Music]': '[音乐]',
                    '[Applause]': '[掌声]',
                    '[Laughter]': '[笑声]'
                }
                translated_content = translation_map.get(content, content)
                print(f"   特殊标记: '{content}' → '{translated_content}'")
            else:
                translated_content = translator.translate(content)
                print(f"   翻译结果: '{translated_content}'")
                time.sleep(0.3)
            
            # 将翻译结果分配到原有的字幕块
            distributed_blocks = distribute_translation_to_blocks_fixed(translated_content, blocks)
            result_blocks.extend(distributed_blocks)
            
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
            for block in blocks:
                result_blocks.append({
                    'index': block['index'],
                    'timestamp': block['timestamp'],
                    'content': block['content']
                })
    
    return result_blocks

def rebuild_srt_from_blocks(blocks):
    """从字幕块重建SRT"""
    srt_lines = []
    
    for block in blocks:
        srt_lines.append(str(block['index']))
        srt_lines.append(block['timestamp'])
        srt_lines.append(block['content'])
        srt_lines.append('')
    
    return '\n'.join(srt_lines)

def test_fixed_distribution():
    """测试修复的翻译分配"""
    print("修复的翻译分配测试")
    print("=" * 50)
    
    english_srt = "real_test_improved.en.srt"
    chinese_srt = "fixed_distribution.zh-Hans.srt"
    
    if not os.path.exists(english_srt):
        print(f"❌ 英文SRT文件不存在: {english_srt}")
        return False
    
    print(f"✅ 找到英文SRT文件: {english_srt}")
    
    # 读取英文SRT
    with open(english_srt, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    # 解析为字幕块
    blocks = parse_srt_blocks(srt_content)
    print(f"解析出 {len(blocks)} 个有效字幕块")
    
    # 按完整句子组合
    sentences = group_by_complete_sentences(blocks)
    print(f"组合成 {len(sentences)} 个完整句子")
    
    # 只处理前3个句子进行测试
    test_sentences = sentences[:3]
    print(f"\n测试前 {len(test_sentences)} 个句子:")
    for i, sentence in enumerate(test_sentences, 1):
        print(f"  {i}. '{sentence['content'][:80]}{'...' if len(sentence['content']) > 80 else ''}'")
        print(f"     包含块: {[b['index'] for b in sentence['blocks']]}")
    
    # 翻译并分配
    print(f"\n开始翻译和分配...")
    result_blocks = translate_and_distribute_fixed(test_sentences)
    
    # 重建SRT
    chinese_srt_content = rebuild_srt_from_blocks(result_blocks)
    
    # 保存
    with open(chinese_srt, 'w', encoding='utf-8') as f:
        f.write(chinese_srt_content)
    
    print(f"\n✅ 修复的翻译分配完成")
    print(f"   输出文件: {chinese_srt}")
    print(f"   生成了 {len(result_blocks)} 个字幕块")
    
    # 显示结果
    print("\n修复后的翻译分配结果:")
    print("-" * 60)
    print(chinese_srt_content)
    print("-" * 60)
    
    # 检查中文字符
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', chinese_srt_content)
    print(f"中文字符数: {len(chinese_chars)}")
    
    return True

if __name__ == "__main__":
    test_fixed_distribution()
