#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量翻译优化器 - 一次性翻译多个句子，大幅提升速度
"""

import os
import re
import time
import requests
import json

class BatchGeminiTranslator:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
    
    def batch_translate(self, sentences, batch_size=5):
        """批量翻译多个句子"""
        # 构建批量翻译的prompt
        sentences_text = ""
        for i, sentence in enumerate(sentences, 1):
            sentences_text += f"{i}. {sentence}\n"
        
        prompt = f"""请将以下{len(sentences)}个英文句子翻译成中文，要求：
1. 翻译要自然流畅
2. 保持原文的语气和情感
3. 如果是歌词，保持韵律感
4. 严格按照序号返回翻译结果
5. 格式：序号. 中文翻译

英文原文：
{sentences_text}

中文翻译："""

        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.3,
                "topK": 1,
                "topP": 1,
                "maxOutputTokens": 2048,
            }
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers=headers,
                data=json.dumps(payload),
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text']
                    return self.parse_batch_result(content, len(sentences))
                else:
                    raise Exception("Gemini API返回格式错误")
            else:
                raise Exception(f"Gemini API错误: {response.status_code}")
                
        except Exception as e:
            raise Exception(f"Gemini批量翻译失败: {str(e)}")
    
    def parse_batch_result(self, content, expected_count):
        """解析批量翻译结果"""
        content = content.strip()
        
        # 按行分割
        lines = content.split('\n')
        translations = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 匹配格式：数字. 翻译内容
            match = re.match(r'^(\d+)\.\s*(.+)$', line)
            if match:
                index = int(match.group(1))
                translation = match.group(2).strip()
                translations.append((index, translation))
        
        # 按序号排序并提取翻译内容
        translations.sort(key=lambda x: x[0])
        result = [t[1] for t in translations]
        
        # 如果数量不匹配，返回错误
        if len(result) != expected_count:
            raise Exception(f"翻译结果数量不匹配: 期望{expected_count}个，实际{len(result)}个")
        
        return result

class BatchSubtitleTranslator:
    def __init__(self, gemini_api_key):
        self.gemini_api_key = gemini_api_key
        self.batch_translator = BatchGeminiTranslator(gemini_api_key)
    
    def extract_complete_sentences(self, srt_content):
        """提取完整句子"""
        print("提取完整句子...")
        
        # 提取所有字幕内容
        subtitle_lines = []
        lines = srt_content.split('\n')
        
        for line in lines:
            line = line.strip()
            if (line.isdigit() or '-->' in line or not line):
                continue
            subtitle_lines.append(line)
        
        # 合并所有内容
        full_text = ' '.join(subtitle_lines)
        print(f"   合并文本长度: {len(full_text)} 字符")
        
        # 按句号分割句子
        sentences = re.split(r'(?<=[.!?])\s+', full_text.strip())
        sentences = [s.strip() for s in sentences if s.strip()]
        
        print(f"   分割出 {len(sentences)} 个句子")
        return sentences
    
    def split_long_text(self, text, max_length=41):
        """翻译后检查41字符限制，超过才拆分"""
        if len(text) <= max_length:
            return [text]
        
        parts = []
        while len(text) > max_length:
            split_pos = max_length
            
            # 向前查找合适的断点
            for i in range(max_length - 1, max(0, max_length - 10), -1):
                if text[i] in '，。！？；：、 ':
                    split_pos = i + 1
                    break
            
            parts.append(text[:split_pos].strip())
            text = text[split_pos:].strip()
        
        if text:
            parts.append(text)
        
        return parts
    
    def batch_translate_sentences(self, sentences, batch_size=5):
        """批量翻译句子，大幅提升速度"""
        print(f"开始批量翻译 {len(sentences)} 个句子...")
        print(f"批量大小: {batch_size} 句/次")
        print("=" * 50)
        
        results = []
        total_batches = (len(sentences) + batch_size - 1) // batch_size
        
        for batch_idx in range(0, len(sentences), batch_size):
            batch_sentences = sentences[batch_idx:batch_idx + batch_size]
            current_batch = (batch_idx // batch_size) + 1
            
            print(f"\n批次 {current_batch}/{total_batches}: 翻译 {len(batch_sentences)} 个句子")
            
            # 显示当前批次的句子
            for i, sentence in enumerate(batch_sentences, 1):
                print(f"   {i}. '{sentence[:50]}{'...' if len(sentence) > 50 else ''}'")
            
            try:
                start_time = time.time()
                
                # 批量翻译
                batch_translations = self.batch_translator.batch_translate(batch_sentences)
                
                end_time = time.time()
                print(f"   批量翻译耗时: {end_time - start_time:.2f}秒")
                print(f"   平均每句: {(end_time - start_time) / len(batch_sentences):.2f}秒")
                
                # 处理翻译结果
                for i, (original, translated) in enumerate(zip(batch_sentences, batch_translations)):
                    print(f"   结果{i+1}: '{translated}' ({len(translated)}字符)")
                    
                    # 检查41字符限制
                    if len(translated) > 41:
                        print(f"     ⚠️  超过41字符，拆分")
                        split_parts = self.split_long_text(translated, max_length=41)
                        print(f"     拆分为: {split_parts}")
                        
                        results.append({
                            'original': original,
                            'translated': translated,
                            'split_parts': split_parts,
                            'needs_split': True
                        })
                    else:
                        results.append({
                            'original': original,
                            'translated': translated,
                            'split_parts': [translated],
                            'needs_split': False
                        })
                
                # 避免API限制
                if current_batch < total_batches:
                    time.sleep(1)
                    
            except Exception as e:
                print(f"   ❌ 批量翻译失败: {e}")
                print(f"   降级到单句翻译...")
                
                # 降级到单句翻译
                for original in batch_sentences:
                    try:
                        single_translation = self.batch_translator.batch_translate([original])
                        translated = single_translation[0]
                        
                        results.append({
                            'original': original,
                            'translated': translated,
                            'split_parts': [translated],
                            'needs_split': False
                        })
                        time.sleep(0.3)
                        
                    except Exception as single_e:
                        print(f"   单句翻译也失败: {single_e}")
                        results.append({
                            'original': original,
                            'translated': original,
                            'split_parts': [original],
                            'needs_split': False,
                            'error': str(single_e)
                        })
        
        # 统计结果
        success_count = sum(1 for r in results if 'error' not in r)
        split_count = sum(1 for r in results if r['needs_split'])
        
        print(f"\n" + "=" * 50)
        print(f"批量翻译完成统计:")
        print(f"   总句子数: {len(sentences)}")
        print(f"   翻译成功: {success_count}")
        print(f"   翻译失败: {len(results) - success_count}")
        print(f"   需要拆分: {split_count}")
        print(f"   总批次数: {total_batches}")
        
        return results

def test_batch_translation():
    """测试批量翻译优化"""
    print("批量翻译优化测试")
    print("=" * 60)
    
    # 使用Gemini API密钥
    gemini_api_key = "AIzaSyBrEmZW9wqmyil7xPsbK2fgMb7LjII8NlQ"
    
    # 初始化批量翻译器
    translator = BatchSubtitleTranslator(gemini_api_key)
    
    # 读取之前的英文SRT文件
    srt_file = "complete_english.en.srt"
    
    if not os.path.exists(srt_file):
        print(f"❌ SRT文件不存在: {srt_file}")
        return False
    
    print(f"✅ 找到SRT文件: {srt_file}")
    
    # 读取SRT文件
    with open(srt_file, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    # 提取完整句子
    sentences = translator.extract_complete_sentences(srt_content)
    
    # 测试不同的批量大小
    batch_sizes = [3, 5, 8]
    
    for batch_size in batch_sizes:
        print(f"\n" + "=" * 60)
        print(f"测试批量大小: {batch_size}")
        print("=" * 60)
        
        start_time = time.time()
        
        # 批量翻译（只测试前15个句子）
        test_sentences = sentences[:15]
        results = translator.batch_translate_sentences(test_sentences, batch_size=batch_size)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n批量大小 {batch_size} 的性能:")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   平均每句: {total_time / len(test_sentences):.2f}秒")
        print(f"   翻译速度: {len(test_sentences) / total_time:.2f} 句/秒")
        
        # 保存结果
        output_file = f"batch_test_{batch_size}.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            for i, result in enumerate(results, 1):
                f.write(f"{i}. {result['original']}\n")
                f.write(f"   → {result['translated']}\n\n")
        
        print(f"   结果已保存到: {output_file}")
    
    print(f"\n🎉 批量翻译优化测试完成！")
    return True

if __name__ == "__main__":
    test_batch_translation()
