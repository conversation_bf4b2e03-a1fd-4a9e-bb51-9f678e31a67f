#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试：英文字幕下载 + VTT转SRT + 翻译为中文
"""

import os
import re
import time
import subprocess
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def should_skip_line(line):
    """检查是否需要跳过当前行（无用信息）"""
    if (
        not line  # 空行
        or line.startswith("WEBVTT")  # VTT 头部
        or line.startswith("Kind:")  # 字幕类型
        or line.startswith("Language:")  # 语言信息
    ):
        return True
    return False

def split_long_lines_to_time_blocks(text, max_length=26):
    """将长字幕拆分为多个时间块"""
    split_texts = []
    while len(text) > max_length:
        split_texts.append(text[:max_length])
        text = text[max_length:]
    split_texts.append(text)
    return split_texts

def convert_vtt_to_srt_improved(vtt_file_path, srt_file_path, max_length=26):
    """改进的VTT转SRT"""
    print(f"步骤2: VTT转SRT - {vtt_file_path} -> {srt_file_path}")
    
    try:
        if not os.path.exists(vtt_file_path):
            print(f"❌ VTT文件不存在: {vtt_file_path}")
            return False

        with open(vtt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        cleaned_lines = []
        block = []
        index_counter = 1
        last_line_text = None
        duplicate_count = 0
        split_count = 0

        for line in lines:
            line = line.strip()

            # 跳过头部信息
            if should_skip_line(line):
                continue

            # 如果这是时间戳行
            if '-->' in line:
                # 先把上一个block写入（如果有内容）
                if block and len(block) > 1:
                    content_lines = [content.strip() for content in block[1:] if content.strip()]
                    if content_lines:
                        cleaned_lines.append(str(index_counter))
                        cleaned_lines.append(block[0])  # 时间戳
                        for content in content_lines:
                            cleaned_lines.append(content)
                        cleaned_lines.append("")  # 添加空行
                        index_counter += 1
                    
                block = []

                # 修正vtt->srt时间戳
                line = line.replace('.', ',')
                line = re.sub(r'\s*align:\w+|\s*position:\d+%', '', line).strip()
                block.append(line)
                
            else:
                # 字幕内容行
                text = re.sub(r'<[^>]+>', '', line)
                text = re.sub(r'<\d+:\d+:\d+\.\d+>', '', text)
                text = text.strip()
                
                if text and text != last_line_text:
                    if len(text) > max_length:
                        split_texts = split_long_lines_to_time_blocks(text, max_length)
                        block.extend(split_texts)
                        split_count += 1
                    else:
                        block.append(text)
                    last_line_text = text
                elif text == last_line_text:
                    duplicate_count += 1

        # 处理最后一个block
        if block and len(block) > 1:
            content_lines = [content.strip() for content in block[1:] if content.strip()]
            if content_lines:
                cleaned_lines.append(str(index_counter))
                cleaned_lines.append(block[0])
                for content in content_lines:
                    cleaned_lines.append(content)
                cleaned_lines.append("")

        # 写入SRT文件
        with open(srt_file_path, 'w', encoding='utf-8') as sf:
            for cl in cleaned_lines:
                sf.write(cl + '\n')

        print(f"✅ VTT转SRT完成")
        print(f"   有效字幕块: {index_counter - 1}")
        print(f"   去重数量: {duplicate_count}")
        print(f"   拆分数量: {split_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ VTT转SRT出错: {e}")
        return False

def translate_srt_file(english_srt_path, chinese_srt_path):
    """翻译英文SRT文件为中文SRT文件"""
    print(f"步骤3: 翻译SRT - {english_srt_path} -> {chinese_srt_path}")
    
    try:
        if not os.path.exists(english_srt_path):
            print(f"❌ 英文SRT文件不存在: {english_srt_path}")
            return False

        with open(english_srt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        translated_lines = []
        translate_count = 0
        
        for line in lines:
            line = line.rstrip('\n\r')
            
            # 保留序号、时间戳和空行
            if (line.strip().isdigit() or 
                '-->' in line or 
                line.strip() == ''):
                translated_lines.append(line)
            else:
                # 翻译字幕内容行
                try:
                    clean_text = re.sub(r'<[^>]+>', '', line)
                    if clean_text.strip():
                        print(f"   翻译中: '{clean_text}'")
                        translated_text = translator.translate(clean_text)
                        translated_lines.append(translated_text)
                        print(f"   翻译结果: '{translated_text}'")
                        translate_count += 1
                        time.sleep(0.3)  # 避免API限制
                    else:
                        translated_lines.append(line)
                except Exception as e:
                    print(f"   ⚠️  翻译失败，保留原文: {line} (错误: {e})")
                    translated_lines.append(line)

        # 保存翻译后的中文SRT文件
        with open(chinese_srt_path, 'w', encoding='utf-8') as f:
            for line in translated_lines:
                f.write(line + '\n')

        print(f"✅ SRT翻译完成")
        print(f"   翻译行数: {translate_count}")
        print(f"   文件大小: {os.path.getsize(chinese_srt_path)} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ SRT翻译出错: {e}")
        return False

def convert_srt_to_txt(srt_file_path, txt_file_path):
    """将SRT文件转换为纯文本"""
    print(f"步骤4: SRT转TXT - {srt_file_path} -> {txt_file_path}")
    
    try:
        if not os.path.exists(srt_file_path):
            print(f"❌ SRT文件不存在: {srt_file_path}")
            return False

        with open(srt_file_path, 'r', encoding='utf-8') as srt_file:
            lines = srt_file.readlines()

        cleaned_lines = []
        for line in lines:
            # 跳过字幕序号和时间戳，只保留字幕文本
            if line.strip().isdigit() or '-->' in line:
                continue
            clean_line = re.sub(r"<[^>]+>", "", line).strip()
            if clean_line:
                cleaned_lines.append(clean_line)

        with open(txt_file_path, 'w', encoding='utf-8') as txt_file:
            txt_file.write(' '.join(cleaned_lines))

        print(f"✅ SRT转TXT完成")
        print(f"   文件大小: {os.path.getsize(txt_file_path)} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ SRT转TXT出错: {e}")
        return False

def download_english_subtitle(video_url, output_name):
    """步骤1: 下载英文字幕"""
    print(f"步骤1: 下载英文字幕 - {video_url}")
    
    # 构建下载命令
    command = [
        'yt-dlp',
        '--write-auto-sub',
        '--skip-download',
        '--sub-lang', 'en',
        '--sub-format', 'vtt',
        '--socket-timeout', '60',
        '-o', f'{output_name}.%(ext)s',
        video_url
    ]
    
    print(f"执行命令: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ 英文字幕下载失败: {result.stderr}")
            return None
        
        # 检查生成的VTT文件
        expected_vtt_path = f'{output_name}.en.vtt'
        if os.path.exists(expected_vtt_path):
            file_size = os.path.getsize(expected_vtt_path)
            print(f"✅ 英文字幕下载成功: {expected_vtt_path}")
            print(f"   文件大小: {file_size} 字节")
            return expected_vtt_path
        else:
            print(f"❌ VTT文件未生成: {expected_vtt_path}")
            return None
            
    except Exception as e:
        print(f"❌ 下载过程出错: {e}")
        return None

def test_complete_process():
    """测试完整流程：英文下载 + VTT转SRT + 翻译"""
    print("完整流程测试：英文字幕下载 + 翻译")
    print("=" * 50)
    
    # 测试视频
    video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    output_name = "complete_test"
    
    print(f"测试视频: {video_url}")
    print(f"输出前缀: {output_name}")
    
    # 步骤1: 下载英文字幕
    vtt_path = download_english_subtitle(video_url, output_name)
    if not vtt_path:
        print("❌ 英文字幕下载失败，测试终止")
        return False
    
    # 步骤2: VTT转SRT
    english_srt_path = f'{output_name}.en.srt'
    if not convert_vtt_to_srt_improved(vtt_path, english_srt_path, max_length=26):
        print("❌ VTT转SRT失败，测试终止")
        return False
    
    # 步骤3: 翻译为中文SRT
    chinese_srt_path = f'{output_name}.zh-Hans.srt'
    if not translate_srt_file(english_srt_path, chinese_srt_path):
        print("❌ SRT翻译失败，测试终止")
        return False
    
    # 步骤4: 转换为TXT
    chinese_txt_path = f'{output_name}.zh-Hans.txt'
    if not convert_srt_to_txt(chinese_srt_path, chinese_txt_path):
        print("❌ SRT转TXT失败")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 完整流程测试成功！")
    print("生成的文件:")
    print(f"   英文VTT: {vtt_path}")
    print(f"   英文SRT: {english_srt_path}")
    print(f"   中文SRT: {chinese_srt_path}")
    print(f"   中文TXT: {chinese_txt_path}")
    
    # 验证最终结果
    print("\n=== 验证最终结果 ===")
    if os.path.exists(chinese_srt_path):
        with open(chinese_srt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', content)
        chinese_count = len(chinese_chars)
        
        print(f"✅ 中文SRT文件存在")
        print(f"   文件大小: {os.path.getsize(chinese_srt_path)} 字节")
        print(f"   中文字符数: {chinese_count}")
        
        # 显示前几行
        lines = content.split('\n')[:15]
        print("   前15行内容:")
        for i, line in enumerate(lines, 1):
            print(f"   {i:2d}: {line}")
        
        if chinese_count > 50:
            print("✅ 翻译质量验证通过")
            return True
        else:
            print("⚠️  中文字符数量较少，可能翻译不完整")
            return False
    else:
        print("❌ 中文SRT文件不存在")
        return False

def main():
    """主函数"""
    return test_complete_process()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
