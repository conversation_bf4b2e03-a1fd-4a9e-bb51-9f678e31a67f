#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单独测试翻译功能
"""

import os
from test_english_subtitle_simple import translate_english_srt_to_chinese

def main():
    print("测试SRT翻译功能")
    print("=" * 30)
    
    english_srt = 'test_subtitles/【TestChannel】Test Video.en.srt'
    chinese_srt = 'test_subtitles/【TestChannel】Test Video.zh-Hans.srt'
    
    if not os.path.exists(english_srt):
        print(f"❌ 英文SRT文件不存在: {english_srt}")
        return 1
    
    print(f"✅ 找到英文SRT文件: {english_srt}")
    
    # 创建一个小的测试文件（只翻译前几个字幕块）
    print("创建测试用的小文件...")
    with open(english_srt, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 只保留前30行进行测试
    test_lines = lines[:30]
    test_srt = 'test_subtitles/test_short.en.srt'
    with open(test_srt, 'w', encoding='utf-8') as f:
        f.writelines(test_lines)
    
    print(f"✅ 创建测试文件: {test_srt}")
    print(f"   包含 {len(test_lines)} 行")
    
    # 翻译测试文件
    test_chinese_srt = 'test_subtitles/test_short.zh-Hans.srt'
    print(f"开始翻译: {test_srt} → {test_chinese_srt}")
    
    result = translate_english_srt_to_chinese(test_srt, test_chinese_srt)
    
    if result:
        print("✅ 翻译成功！")
        
        # 检查翻译结果
        if os.path.exists(test_chinese_srt):
            with open(test_chinese_srt, 'r', encoding='utf-8') as f:
                chinese_content = f.read()
            
            print(f"✅ 中文SRT文件已生成: {test_chinese_srt}")
            print(f"   文件大小: {len(chinese_content)} 字符")
            
            # 显示前几行
            print("\n前10行内容:")
            lines = chinese_content.split('\n')[:10]
            for i, line in enumerate(lines, 1):
                print(f"   {i:2d}: {line}")
            
            return 0
        else:
            print("❌ 中文SRT文件未生成")
            return 1
    else:
        print("❌ 翻译失败")
        return 1

if __name__ == "__main__":
    exit(main())
