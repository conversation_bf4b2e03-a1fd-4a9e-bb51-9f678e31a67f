#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试翻译现有的英文SRT文件
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def translate_srt_file(english_srt_path, chinese_srt_path):
    """翻译英文SRT文件为中文SRT文件"""
    print(f"开始翻译: {english_srt_path} -> {chinese_srt_path}")
    
    try:
        if not os.path.exists(english_srt_path):
            print(f"❌ 英文SRT文件不存在: {english_srt_path}")
            return False

        with open(english_srt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        print(f"原文件共 {len(lines)} 行")

        translated_lines = []
        translate_count = 0
        skip_count = 0
        
        for line_num, line in enumerate(lines, 1):
            line = line.rstrip('\n\r')
            
            # 保留序号、时间戳和空行
            if (line.strip().isdigit() or 
                '-->' in line or 
                line.strip() == ''):
                translated_lines.append(line)
                skip_count += 1
            else:
                # 翻译字幕内容行
                try:
                    clean_text = re.sub(r'<[^>]+>', '', line)
                    if clean_text.strip():
                        print(f"   第{line_num}行翻译中: '{clean_text}'")
                        translated_text = translator.translate(clean_text)
                        translated_lines.append(translated_text)
                        print(f"   翻译结果: '{translated_text}'")
                        translate_count += 1
                        time.sleep(0.3)  # 避免API限制
                    else:
                        translated_lines.append(line)
                        skip_count += 1
                except Exception as e:
                    print(f"   ⚠️  翻译失败，保留原文: {line} (错误: {e})")
                    translated_lines.append(line)

        # 保存翻译后的中文SRT文件
        with open(chinese_srt_path, 'w', encoding='utf-8') as f:
            for line in translated_lines:
                f.write(line + '\n')

        print(f"\n✅ SRT翻译完成")
        print(f"   翻译行数: {translate_count}")
        print(f"   跳过行数: {skip_count}")
        print(f"   输出文件: {chinese_srt_path}")
        print(f"   文件大小: {os.path.getsize(chinese_srt_path)} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ SRT翻译出错: {e}")
        return False

def verify_chinese_content(chinese_srt_path):
    """验证中文内容"""
    print(f"\n=== 验证中文内容 ===")
    
    try:
        with open(chinese_srt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', content)
        chinese_count = len(chinese_chars)
        
        print(f"中文字符数: {chinese_count}")
        
        # 显示前几个字幕块
        blocks = content.strip().split('\n\n')
        print(f"总字幕块数: {len(blocks)}")
        
        print("\n前5个字幕块:")
        for i, block in enumerate(blocks[:5], 1):
            lines = block.strip().split('\n')
            print(f"\n字幕块 {i}:")
            for j, line in enumerate(lines):
                print(f"   {j+1}: {line}")
        
        if chinese_count > 50:
            print(f"\n✅ 翻译质量验证通过 (中文字符: {chinese_count})")
            return True
        else:
            print(f"\n⚠️  中文字符数量较少: {chinese_count}")
            return False
            
    except Exception as e:
        print(f"❌ 验证中文内容时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("直接翻译现有英文SRT文件测试")
    print("=" * 50)
    
    # 使用现有的处理好的英文SRT文件
    english_srt = "real_test_improved.en.srt"
    chinese_srt = "real_test_translated.zh-Hans.srt"
    
    if not os.path.exists(english_srt):
        print(f"❌ 英文SRT文件不存在: {english_srt}")
        return 1
    
    print(f"✅ 找到英文SRT文件: {english_srt}")
    print(f"   文件大小: {os.path.getsize(english_srt)} 字节")
    
    # 显示英文SRT的前几行
    print("\n英文SRT前10行:")
    with open(english_srt, 'r', encoding='utf-8') as f:
        lines = f.readlines()[:10]
    for i, line in enumerate(lines, 1):
        print(f"   {i:2d}: {line.rstrip()}")
    
    # 翻译
    if not translate_srt_file(english_srt, chinese_srt):
        print("❌ 翻译失败")
        return 1
    
    # 验证
    if verify_chinese_content(chinese_srt):
        print("\n🎉 翻译测试成功！")
        print(f"生成文件: {chinese_srt}")
        return 0
    else:
        print("\n⚠️  翻译质量需要改进")
        return 0

if __name__ == "__main__":
    exit(main())
