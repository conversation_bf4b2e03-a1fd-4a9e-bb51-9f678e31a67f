import os
import subprocess
import re
import requests
from config import logger, USE_PROXY, PROXY_URL
from utils import sanitize_filename

def get_thumbnail_url(video_id):
    """
    获取视频的高质量缩略图URL
    
    参数:
    - video_id: YouTube视频ID
    
    返回:
    - 缩略图URL
    """
    # YouTube的缩略图有几种格式，从高质量到低质量
    # maxresdefault.jpg (1920x1080)
    # sddefault.jpg (640x480)
    # hqdefault.jpg (480x360)
    # mqdefault.jpg (320x180)
    # default.jpg (120x90)
    
    # 先尝试获取最高质量的缩略图
    thumbnail_formats = [
        f"https://i.ytimg.com/vi/{video_id}/maxresdefault.jpg",
        f"https://i.ytimg.com/vi/{video_id}/sddefault.jpg",
        f"https://i.ytimg.com/vi/{video_id}/hqdefault.jpg"
    ]
    
    # 使用yt-dlp获取视频信息，包括缩略图URL
    try:
        command = ['yt-dlp', '--cookies-from-browser', 'firefox']
        if USE_PROXY:
            command += ['--proxy', PROXY_URL]
        command += ['--skip-download', '--print', 'thumbnail', f'https://www.youtube.com/watch?v={video_id}']
        
        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            # 如果yt-dlp成功获取到缩略图URL，就使用它
            return result.stdout.strip()
        else:
            # 如果失败，使用预定义的URL格式
            logger.warning(f"通过yt-dlp获取缩略图URL失败，使用默认格式")
            return thumbnail_formats[0]  # 返回最高质量的缩略图URL
    except Exception as e:
        logger.error(f"获取缩略图URL时出错: {e}")
        return thumbnail_formats[0]  # 失败时返回最高质量的缩略图URL

def download_thumbnail(video_id, channel_name, video_title, save_dir="thumbnails"):
    """
    下载视频缩略图
    
    参数:
    - video_id: YouTube视频ID
    - channel_name: 频道名称
    - video_title: 视频标题
    - save_dir: 保存目录，默认为"thumbnails"
    
    返回:
    - 成功返回保存的缩略图路径，失败返回None
    """
    try:
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 获取缩略图URL
        thumbnail_url = get_thumbnail_url(video_id)
        
        # 创建文件名
        sanitized_title = sanitize_filename(video_title)
        if len(sanitized_title) > 30:
            sanitized_title = sanitized_title[:30]
        filename = f"【{channel_name}】{sanitized_title}_{video_id}.jpg"
        filepath = os.path.join(save_dir, filename)
        
        # 下载缩略图
        session = requests.Session()
        if USE_PROXY:
            proxies = {
                'http': PROXY_URL,
                'https': PROXY_URL
            }
            session.proxies.update(proxies)
        
        response = session.get(thumbnail_url, stream=True, timeout=30)
        if response.status_code == 200:
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # 检查文件是否成功下载
            if os.path.exists(filepath) and os.path.getsize(filepath) > 1024:  # 至少1KB
                logger.warning(f"成功下载缩略图: {filepath}")
                return filepath
            else:
                logger.error(f"缩略图下载失败或文件过小: {filepath}")
                if os.path.exists(filepath):
                    os.remove(filepath)  # 删除不完整的文件
                return None
        else:
            logger.error(f"下载缩略图失败，HTTP状态码: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"下载缩略图时发生错误: {e}")
        return None

if __name__ == "__main__":
    # 示例用法
    video_id = "dQw4w9WgXcQ"  # 示例视频ID
    channel_name = "测试频道"
    video_title = "测试视频标题"
    
    thumbnail_path = download_thumbnail(video_id, channel_name, video_title)
    if thumbnail_path:
        print(f"缩略图已下载到: {thumbnail_path}")
    else:
        print("缩略图下载失败")
