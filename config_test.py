import os
import logging

# 获取当前脚本所在的绝对路径
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

# 配置日志
logging.basicConfig(
    level=logging.WARNING,
    format='%(message)s',
    handlers=[
        logging.FileHandler(os.path.join(CURRENT_DIR, "test_script.log"), encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 测试用的字幕目录
VTT_HAN_FOLDER = "test_fallback"
SRT_HAN_FOLDER = "test_fallback"
os.makedirs(VTT_HAN_FOLDER, exist_ok=True)
os.makedirs(SRT_HAN_FOLDER, exist_ok=True)

# 代理配置
USE_PROXY = False
PROXY_URL = "http://127.0.0.1:7987"
