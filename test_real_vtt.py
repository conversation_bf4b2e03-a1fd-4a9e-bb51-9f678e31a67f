#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实VTT文件的处理
使用从YouTube下载的真实VTT文件测试：VTT→SRT→去重→字数限制→翻译
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def should_skip_line(line):
    """检查是否需要跳过当前行（无用信息）"""
    if (
        not line  # 空行
        or line.startswith("WEBVTT")  # VTT 头部
        or line.startswith("Kind:")  # 字幕类型
        or line.startswith("Language:")  # 语言信息
    ):
        return True
    return False

def split_long_lines_to_time_blocks(text, max_length=26):
    """将长字幕拆分为多个时间块"""
    split_texts = []
    while len(text) > max_length:
        split_texts.append(text[:max_length])
        text = text[max_length:]
    split_texts.append(text)
    return split_texts

def convert_real_vtt_to_srt(vtt_file_path, srt_file_path, max_length=26):
    """
    将真实的VTT文件转换为SRT，包含去重和字数限制
    """
    print(f"=== 转换真实VTT文件 ===")
    print(f"输入: {vtt_file_path}")
    print(f"输出: {srt_file_path}")
    print(f"最大字数: {max_length}")
    
    try:
        if not os.path.exists(vtt_file_path):
            print(f"❌ VTT文件不存在: {vtt_file_path}")
            return False

        with open(vtt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        print(f"原始VTT文件共 {len(lines)} 行")

        cleaned_lines = []
        block = []
        index_counter = 1
        last_line_text = None
        duplicate_count = 0
        split_count = 0

        for line_num, line in enumerate(lines, 1):
            line = line.strip()

            # 跳过头部信息
            if should_skip_line(line):
                continue

            # 如果这是时间戳行
            if '-->' in line:
                # 先把上一个block写入
                if block:
                    cleaned_lines.append(str(index_counter))
                    cleaned_lines.append(block[0])  # 时间戳
                    for c in block[1:]:
                        cleaned_lines.append(c)
                    cleaned_lines.append("")  # 添加空行
                    index_counter += 1
                    block = []

                # 修正vtt->srt时间戳格式
                line = line.replace('.', ',')  # 00:00:01.000 -> 00:00:01,000
                # 移除VTT特有的属性
                line = re.sub(r'\s*align:\w+|\s*position:\d+%', '', line).strip()
                block.append(line)
                
            else:
                # 字幕内容行
                # 去掉HTML标签和时间标记
                text = re.sub(r'<[^>]+>', '', line)  # 去掉HTML标签
                text = re.sub(r'<\d+:\d+:\d+\.\d+>', '', text)  # 去掉时间标记
                text = text.strip()
                
                if text and text != last_line_text:
                    # 检查字数限制并拆分
                    if len(text) > max_length:
                        print(f"   长字幕拆分: '{text}' (长度: {len(text)})")
                        split_texts = split_long_lines_to_time_blocks(text, max_length)
                        block.extend(split_texts)
                        split_count += 1
                        print(f"   拆分为: {split_texts}")
                    else:
                        block.append(text)
                    last_line_text = text
                elif text == last_line_text:
                    duplicate_count += 1
                    print(f"   去重: 跳过重复字幕 '{text}'")

        # 处理最后一个block
        if block:
            cleaned_lines.append(str(index_counter))
            cleaned_lines.append(block[0])
            for c in block[1:]:
                cleaned_lines.append(c)
            cleaned_lines.append("")

        # 写入SRT文件
        with open(srt_file_path, 'w', encoding='utf-8') as sf:
            for cl in cleaned_lines:
                sf.write(cl + '\n')

        print(f"✅ VTT转SRT完成")
        print(f"   总字幕块数: {index_counter - 1}")
        print(f"   去重数量: {duplicate_count}")
        print(f"   拆分数量: {split_count}")
        print(f"   输出文件大小: {os.path.getsize(srt_file_path)} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ VTT转SRT出错: {e}")
        return False

def verify_srt_format(srt_file_path, max_length=26):
    """验证SRT格式和字数限制"""
    print(f"\n=== 验证SRT格式 ===")
    
    try:
        with open(srt_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按空行分割字幕块
        blocks = content.strip().split('\n\n')
        
        print(f"总字幕块数: {len(blocks)}")
        
        format_errors = 0
        length_errors = 0
        
        # 检查前10个块
        for i, block in enumerate(blocks[:10], 1):
            lines = block.strip().split('\n')
            print(f"\n字幕块 {i}:")
            
            if len(lines) < 3:
                print(f"   ❌ 格式错误: 行数不足 ({len(lines)} < 3)")
                format_errors += 1
                continue
            
            # 检查序号
            if not lines[0].isdigit():
                print(f"   ❌ 序号错误: '{lines[0]}'")
                format_errors += 1
            else:
                print(f"   ✅ 序号: {lines[0]}")
            
            # 检查时间戳
            if '-->' not in lines[1]:
                print(f"   ❌ 时间戳错误: '{lines[1]}'")
                format_errors += 1
            else:
                print(f"   ✅ 时间戳: {lines[1]}")
            
            # 检查字幕内容和字数
            subtitle_lines = lines[2:]
            for j, subtitle_line in enumerate(subtitle_lines):
                text_length = len(subtitle_line)
                print(f"   ✅ 内容{j+1}: '{subtitle_line}' (长度: {text_length})")
                
                if text_length > max_length:
                    print(f"   ⚠️  超长警告: {text_length} > {max_length}")
                    length_errors += 1
        
        print(f"\n验证结果:")
        print(f"   格式错误: {format_errors}")
        print(f"   超长字幕: {length_errors}")
        
        if format_errors == 0 and length_errors == 0:
            print("   ✅ SRT格式完全正确！")
            return True
        else:
            print("   ⚠️  发现问题，需要调整")
            return False
        
    except Exception as e:
        print(f"❌ 验证SRT格式时出错: {e}")
        return False

def translate_srt_sample(english_srt_path, chinese_srt_path, max_blocks=5):
    """翻译SRT文件的前几个块进行测试"""
    print(f"\n=== 翻译测试 (前{max_blocks}个块) ===")
    
    try:
        with open(english_srt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        translated_lines = []
        block_count = 0
        
        for line in lines:
            line = line.rstrip('\n\r')
            
            # 保留序号、时间戳和空行
            if (line.strip().isdigit() or 
                '-->' in line or 
                line.strip() == ''):
                translated_lines.append(line)
            else:
                # 翻译字幕内容行
                if block_count < max_blocks:
                    try:
                        clean_text = re.sub(r'<[^>]+>', '', line)
                        if clean_text.strip():
                            print(f"   翻译中: '{clean_text}'")
                            translated_text = translator.translate(clean_text)
                            translated_lines.append(translated_text)
                            print(f"   翻译结果: '{translated_text}'")
                            time.sleep(0.3)  # 避免API限制
                            block_count += 1
                        else:
                            translated_lines.append(line)
                    except Exception as e:
                        print(f"   ⚠️  翻译失败，保留原文: {line} (错误: {e})")
                        translated_lines.append(line)
                else:
                    # 超过测试数量，保留原文
                    translated_lines.append(line)

        # 保存翻译后的文件
        with open(chinese_srt_path, 'w', encoding='utf-8') as f:
            for line in translated_lines:
                f.write(line + '\n')

        print(f"✅ 翻译测试完成: {chinese_srt_path}")
        print(f"   翻译块数: {block_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 翻译测试出错: {e}")
        return False

def main():
    """主测试函数"""
    print("真实VTT文件处理测试")
    print("=" * 50)
    
    # 使用真实下载的VTT文件
    vtt_file = "real_test.en.vtt"
    srt_file = "real_test.en.srt"
    chinese_srt_file = "real_test.zh-Hans.srt"
    
    if not os.path.exists(vtt_file):
        print(f"❌ VTT文件不存在: {vtt_file}")
        print("请先运行: yt-dlp --write-auto-sub --skip-download --sub-lang en --sub-format vtt -o \"real_test.%(ext)s\" \"https://www.youtube.com/watch?v=dQw4w9WgXcQ\"")
        return 1
    
    print(f"✅ 找到真实VTT文件: {vtt_file}")
    print(f"   文件大小: {os.path.getsize(vtt_file)} 字节")
    
    # 步骤1: VTT转SRT
    if not convert_real_vtt_to_srt(vtt_file, srt_file, max_length=26):
        print("❌ VTT转SRT失败")
        return 1
    
    # 步骤2: 验证SRT格式
    if not verify_srt_format(srt_file, max_length=26):
        print("⚠️  SRT格式验证有问题，但继续测试翻译")
    
    # 步骤3: 翻译测试
    if not translate_srt_sample(srt_file, chinese_srt_file, max_blocks=5):
        print("❌ 翻译测试失败")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 真实VTT处理测试完成！")
    print("生成的文件:")
    print(f"   原始VTT: {vtt_file}")
    print(f"   英文SRT: {srt_file}")
    print(f"   中文SRT: {chinese_srt_file}")
    print("\n请检查生成的文件确认处理效果。")
    
    return 0

if __name__ == "__main__":
    exit(main())
