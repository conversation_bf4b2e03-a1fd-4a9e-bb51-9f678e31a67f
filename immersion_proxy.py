from flask import Flask, request, Response
import requests
import gzip
import brotli
import zstandard as zstd
import io
import os
import datetime
import time

app = Flask(__name__)
REAL_API = "https://translate.jayogo.com/v1/chat/completions"

# 日志文件路径
LOG_FILE = "proxy_logs.txt"

def log_to_file(message):
    """将消息记录到日志文件，并带上精确的时间戳"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_message = f"[{timestamp}] {message}\n"
    
    # 确保日志文件存在
    if not os.path.exists(os.path.dirname(LOG_FILE)) and os.path.dirname(LOG_FILE):
        os.makedirs(os.path.dirname(LOG_FILE))
    
    # 写入日志
    with open(LOG_FILE, "a", encoding="utf-8") as f:
        f.write(log_message)
    
    # 同时打印到控制台
    print(log_message, end="")

@app.route('/v1/chat/completions', methods=['POST'])
def proxy():
    request_id = int(time.time() * 1000)  # 创建唯一请求ID
    log_to_file(f"=== 新请求 #{request_id} ===")
    
    # 记录请求头
    log_to_file(f"🟡 请求头 #{request_id}:")
    log_to_file(str(dict(request.headers)))
    
    # 记录请求体
    log_to_file(f"🟢 请求内容 #{request_id}:")
    log_to_file(str(request.json))
    
    headers = dict(request.headers)
    headers.pop('Host', None)
    
    # 告诉 Jayogo 不要返回压缩内容
    headers['Accept-Encoding'] = 'identity'
    
    # 发送请求并记录
    log_to_file(f"📤 转发请求到 {REAL_API} #{request_id}")
    start_time = time.time()
    response = requests.post(REAL_API, headers=headers, json=request.json)
    elapsed_time = time.time() - start_time
    log_to_file(f"📥 收到响应 #{request_id} (耗时: {elapsed_time:.2f}秒)")
    
    encoding = response.headers.get('Content-Encoding', '').lower()
    content = response.content
    log_to_file(f"🔍 响应编码: {encoding or '无'} #{request_id}")

    try:
        if encoding == 'gzip':
            content = gzip.decompress(content)
            log_to_file(f"🔓 已解压gzip数据 #{request_id}")
        elif encoding == 'br':
            content = brotli.decompress(content)
            log_to_file(f"🔓 已解压brotli数据 #{request_id}")
        elif encoding == 'zstd':
            dctx = zstd.ZstdDecompressor()
            with dctx.stream_reader(io.BytesIO(content)) as reader:
                content = reader.read()
            log_to_file(f"🔓 已解压zstd数据 #{request_id}")
        elif not encoding:
            log_to_file(f"⏩ 数据未压缩 #{request_id}")
        else:
            log_to_file(f"⚠️ 未知的编码方式: {encoding} #{request_id}")

        decoded = content.decode('utf-8')
        log_to_file(f"🔵 API响应状态码: {response.status_code} #{request_id}")
        
        # 记录响应内容（可能很长，可以选择只记录前N个字符）
        response_preview = decoded[:500] + "..." if len(decoded) > 500 else decoded
        log_to_file(f"🔵 API响应内容预览: #{request_id}\n{response_preview}")
        
        log_to_file(f"✅ 请求处理完成 #{request_id}")
        return Response(
            response=decoded,
            status=response.status_code,
            content_type='application/json'
        )

    except Exception as e:
        log_to_file(f"❌ 解码或解压失败 #{request_id}: {e}")
        return Response(
            response="解压或解码失败。可能是未知编码格式，或响应内容不完整。",
            status=500,
            content_type='text/plain'
        )

if __name__ == '__main__':
    # 启动时记录
    log_to_file("🚀 代理服务器已启动：http://localhost:8080")
    log_to_file(f"📝 日志将保存到: {os.path.abspath(LOG_FILE)}")
    
    # 启动Flask服务
    app.run(host='0.0.0.0', port=8080)
