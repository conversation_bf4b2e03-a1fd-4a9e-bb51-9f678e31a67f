# YouTube视频下载系统 - 项目结构与文件说明

## 项目概述

这是一个专门用于自动监控和下载YouTube频道视频的系统。它能够定期检查指定频道的最新上传，根据关键词筛选视频，下载视频文件及其字幕和封面，并将标题翻译为中文便于管理。该系统具有高度的自动化和容错能力，适合长期运行以持续收集YouTube内容。

## 文件结构与功能

### 核心文件

| 文件名 | 行数 | 功能描述 |
|--------|------|----------|
| `main.py` | 113 | 程序入口点，包含视频重复检测逻辑，启动监控系统 |
| `config.py` | 42 | 集中配置管理，包含路径、API密钥、代理设置和日志配置 |
| `monitor.py` | 142 | 实现视频监控主逻辑，定期检查YouTube频道的最新视频 |
| `downloader.py` | 187 | 实现视频下载的主流程，处理字幕、封面和视频文件的下载 |
| `video_info.py` | 134 | 获取YouTube视频信息，检测视频是否为直播/预告 |

### 辅助功能文件

| 文件名 | 行数 | 功能描述 |
|--------|------|----------|
| `translation.py` | 58 | 处理视频标题翻译，支持自定义API和谷歌翻译作为备选 |
| `subtitle.py` | 372 | 下载和处理视频字幕，转换格式并验证字幕质量 |
| `thumbnail_downloader.py` | 154 | 下载视频缩略图/封面，处理图片保存 |
| `utils.py` | 110 | 提供通用工具函数，管理下载历史和关键词过滤 |

### 测试与扩展功能

| 文件名 | 行数 | 功能描述 |
|--------|------|----------|
| `testapi.py` | 190 | 测试翻译API的功能和性能，分析API使用的实际模型 |
| `filter_test.py` | 87 | 测试视频过滤功能和重复检测逻辑 |
| `immersion_proxy.py` | 109 | 处理代理连接，用于绕过地区限制 |

### 其他支持文件

| 文件名 | 行数 | 功能描述 |
|--------|------|----------|
| `requirements.txt` | 3 | 项目依赖库列表，用于环境配置 |
| `README.md` | 45 | 项目说明文档 |
| `__init__.py` | 2 | 标识模块包，可能包含版本信息 |
| `api.txt` | 16 | API请求/响应样例，用于参考和测试 |

## 系统逻辑流程图

```
[启动] main.py
   │
   ▼
[初始化] 加载配置和日志系统
   │
   ▼
[监控循环] monitor.py::start_video_monitoring()
   │
   ├──► [获取视频列表] video_info.py::get_latest_videos_with_retry()
   │
   ├──► [过滤视频] 根据关键词和历史记录
   │
   └──► [下载流程] downloader.py::download_video()
         │
         ├──► [检查] 是否已下载/是否为直播
         │
         ├──► [翻译] translation.py::translate_title_with_retry()
         │
         ├──► [下载字幕] subtitle.py::process_subtitles()
         │
         ├──► [下载封面] thumbnail_downloader.py::download_thumbnail()
         │
         ├──► [提取时间戳信息] extract_video_timestamp()
         │
         └──► [下载视频] 使用yt-dlp
               │
               └──► [更新历史] utils.py::save_downloaded_history()
```

## 关键技术依赖

1. **yt-dlp**: 核心YouTube视频下载工具，替代已不再维护的youtube-dl
2. **feedparser**: 用于解析YouTube RSS订阅源，获取频道最新视频
3. **httpx**: 现代HTTP客户端，用于API请求
4. **fuzzywuzzy**: 提供字符串模糊匹配功能，用于视频标题相似度比较
5. **googletrans**: Google翻译API的非官方封装
6. **ffprobe**: 用于获取视频时长等元数据信息

## 文件夹结构

```
项目根目录
├── thumbnails/ (存储下载的视频封面)
├── __pycache__/ (Python编译缓存)
└── 各Python模块文件
```

对应的数据存储目录（由config.py配置）:
```
D:/ytb_python_download/
├── vtt_han/ (存储原始VTT格式字幕)
├── srt_han/ (存储转换后的SRT格式字幕)
├── timestamps/ (存储视频时间戳信息)
└── 视频文件(MP4格式)
```

## 系统特性

1. **高度自动化**: 全自动监控和下载，无需人工干预
2. **内容过滤**: 基于关键词和重复检测的智能筛选
3. **本地化**: 视频标题自动翻译为中文
4. **容错机制**: 多级错误处理和重试逻辑
5. **质量保证**: 对字幕等内容进行质量验证
6. **完整归档**: 视频、字幕、封面和元数据的完整存储

## 使用限制与依赖

1. 系统主要针对Windows环境设计（路径格式）
2. 需要安装Firefox浏览器并登录YouTube账号（获取cookies用于绕过限制）
3. 需要ffprobe工具用于视频元数据提取
4. 翻译功能依赖外部API服务
5. 需要稳定的网络连接以访问YouTube

## 改进方向

1. 路径处理优化: 减少硬编码路径，提升跨平台兼容性
2. API密钥管理: 改进敏感信息的存储方式
3. 异步下载: 提高并发性能
4. Web界面: 添加可视化管理功能
5. 智能分类: 基于内容自动分类视频 