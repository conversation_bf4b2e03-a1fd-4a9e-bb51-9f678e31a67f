#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Gemini翻译测试
"""

import os
import time
import requests
import json

class GeminiTranslator:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"
    
    def translate(self, text):
        """使用Gemini翻译文本"""
        prompt = f"""请将以下英文翻译成中文，要求：
1. 翻译要自然流畅
2. 保持原文的语气和情感
3. 如果是歌词，保持韵律感
4. 只返回翻译结果，不要其他解释

英文原文：{text}

中文翻译："""

        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.3,
                "topK": 1,
                "topP": 1,
                "maxOutputTokens": 1024,
            }
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers=headers,
                data=json.dumps(payload),
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text']
                    # 清理返回的内容
                    content = content.strip()
                    # 移除可能的前缀
                    if content.startswith('中文翻译：'):
                        content = content[5:].strip()
                    elif content.startswith('翻译：'):
                        content = content[3:].strip()
                    return content
                else:
                    raise Exception("Gemini API返回格式错误")
            else:
                raise Exception(f"Gemini API错误: {response.status_code} - {response.text}")
                
        except Exception as e:
            raise Exception(f"Gemini翻译失败: {str(e)}")

def test_gemini():
    """测试Gemini翻译"""
    print("Gemini翻译测试")
    print("=" * 40)

    # 使用提供的API密钥
    api_key = "AIzaSyBrEmZW9wqmyil7xPsbK2fgMb7LjII8NlQ"

    print(f"✅ 使用API密钥: {api_key[:10]}...")
    
    # 初始化翻译器
    translator = GeminiTranslator(api_key)
    
    # 测试句子（Rick Roll歌词）
    test_sentences = [
        "We're no strangers to love.",
        "You know the rules and so do I.",
        "Never gonna give you up.",
        "Never gonna let you down.",
        "Never gonna run around and desert you."
    ]
    
    print(f"\n开始测试翻译...")
    
    for i, sentence in enumerate(test_sentences, 1):
        try:
            print(f"\n--- 句子 {i} ---")
            print(f"原文: '{sentence}'")
            
            start_time = time.time()
            translated = translator.translate(sentence)
            end_time = time.time()
            
            print(f"译文: '{translated}'")
            print(f"耗时: {end_time - start_time:.2f}秒")
            print(f"长度: {len(translated)} 字符")
            
            if len(translated) > 41:
                print(f"⚠️  超过41字符限制")
            else:
                print(f"✅ 符合41字符限制")
            
            time.sleep(1)  # 避免API限制
            
        except Exception as e:
            print(f"❌ 翻译失败: {e}")
    
    print(f"\n✅ Gemini翻译测试完成")
    return True

def compare_with_google():
    """与Google Translate对比"""
    print("\n" + "=" * 50)
    print("与Google Translate对比")
    print("=" * 50)

    # 使用提供的API密钥
    api_key = "AIzaSyBrEmZW9wqmyil7xPsbK2fgMb7LjII8NlQ"
    
    # 初始化翻译器
    gemini_translator = GeminiTranslator(api_key)
    
    try:
        from deep_translator import GoogleTranslator
        google_translator = GoogleTranslator(source='en', target='zh-CN')
    except ImportError:
        print("❌ 请安装deep-translator: pip install deep-translator")
        return False
    
    # 测试句子
    test_sentence = "We're no strangers to love. You know the rules and so do I."
    
    print(f"测试句子: '{test_sentence}'")
    print("-" * 50)
    
    # Google Translate
    try:
        print("Google Translate翻译中...")
        google_result = google_translator.translate(test_sentence)
        print(f"Google: '{google_result}' ({len(google_result)} 字符)")
    except Exception as e:
        print(f"Google Translate失败: {e}")
        google_result = None
    
    # Gemini
    try:
        print("Gemini翻译中...")
        gemini_result = gemini_translator.translate(test_sentence)
        print(f"Gemini: '{gemini_result}' ({len(gemini_result)} 字符)")
    except Exception as e:
        print(f"Gemini翻译失败: {e}")
        gemini_result = None
    
    print("-" * 50)
    
    if google_result and gemini_result:
        print("对比分析:")
        print(f"  Google: {len(google_result)} 字符")
        print(f"  Gemini: {len(gemini_result)} 字符")
        
        print("\n哪个翻译更好？")
        print("1. Google翻译更简洁直接")
        print("2. Gemini翻译更自然流畅")
        print("3. 两者各有优势")
    
    return True

if __name__ == "__main__":
    print("Gemini翻译测试工具")
    print("=" * 40)
    print("1. 基本翻译测试")
    print("2. 与Google Translate对比")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            test_gemini()
        elif choice == "2":
            compare_with_google()
        elif choice == "3":
            print("退出测试")
            break
        else:
            print("无效选择，请输入1-3")
    
    print("\n使用说明:")
    print("1. 获取Gemini API密钥: https://makersuite.google.com/app/apikey")
    print("2. 确保有网络连接")
    print("3. API调用可能需要一些时间")
