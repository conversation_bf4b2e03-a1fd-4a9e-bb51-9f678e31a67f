#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的字幕降级策略模块
基于原逻辑，修复空字幕块问题，完善降级策略
"""

import os
import re
import time
import subprocess
from config import logger, USE_PROXY, PROXY_URL, VTT_HAN_FOLDER, SRT_HAN_FOLDER
from utils import sanitize_filename
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def check_subtitle_size(file_path, min_size_kb=1):
    """检查字幕文件是否存在且大小是否符合要求 - 原逻辑保持不变"""
    try:
        if not os.path.exists(file_path):
            logger.warning(f"字幕文件不存在: {file_path}")
            return False
            
        file_size_kb = os.path.getsize(file_path) / 1024  # 转换为KB
        if file_size_kb < min_size_kb:
            logger.warning(f"字幕文件大小不足 {min_size_kb}KB: {file_path} (当前大小: {file_size_kb:.2f}KB)")
            return False
            
        logger.warning(f"字幕文件检查通过: {file_path} (大小: {file_size_kb:.2f}KB)")
        return True
    except Exception as e:
        logger.error(f"检查字幕文件时出错: {e}")
        return False

def should_skip_line(line):
    """检查是否需要跳过当前行（无用信息） - 原逻辑保持不变"""
    if (
        not line  # 空行
        or line.startswith("WEBVTT")  # VTT 头部
        or line.startswith("Kind:")  # 字幕类型
        or line.startswith("Language:")  # 语言信息
    ):
        return True
    return False

def split_long_lines_to_time_blocks(text, max_length=50):
    """将长字幕拆分为多个时间块 - 原逻辑保持不变"""
    split_texts = []
    while len(text) > max_length:
        split_texts.append(text[:max_length])  # 分割字幕文本
        text = text[max_length:]  # 更新剩余文本
    split_texts.append(text)  # 添加剩余部分
    return split_texts

def convert_vtt_to_srt_improved(vtt_file_path, srt_file_path, max_length=50):
    """
    改进的VTT转SRT - 基于原逻辑，修复空字幕块问题
    """
    try:
        if not os.path.exists(vtt_file_path):
            logger.error(f"VTT 不存在: {vtt_file_path}")
            return False

        with open(vtt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        cleaned_lines = []
        block = []
        index_counter = 1
        last_line_text = None

        for line in lines:
            line = line.strip()

            # 跳过头部 "WEBVTT"等
            if should_skip_line(line):
                continue

            # 如果这是时间戳行
            if '-->' in line:
                # 先把上一个block写入（如果有内容）
                if block and len(block) > 1:  # 确保有时间戳和内容
                    # 检查是否有实际的字幕内容
                    has_content = any(content.strip() for content in block[1:])
                    if has_content:
                        cleaned_lines.append(str(index_counter))
                        cleaned_lines.append(block[0])  # 时间戳
                        for c in block[1:]:
                            if c.strip():  # 只添加非空内容
                                cleaned_lines.append(c)
                        cleaned_lines.append("")  # 添加空行
                        index_counter += 1
                    block = []  # 清空block

                # 修正vtt->srt时间戳
                line = line.replace('.', ',')  # 00:00:01.000 -> 00:00:01,000
                # 移除多余的 align: start position:0% 之类
                line = re.sub(r'\s*align:\w+|\s*position:\d+%', '', line).strip()
                block.append(line)
            else:
                # 非时间戳行(字幕内容)
                # 去掉HTML标签和时间标记
                text = re.sub(r'<[^>]+>', '', line)  # 去掉HTML标签
                text = re.sub(r'<\d+:\d+:\d+\.\d+>', '', text)  # 去掉时间标记
                text = text.strip()
                
                if text and text != last_line_text:
                    # 处理长行
                    splitted = split_long_lines_to_time_blocks(text, max_length=max_length)
                    block.extend(splitted)
                    last_line_text = text

        # 处理最后一个block
        if block and len(block) > 1:
            has_content = any(content.strip() for content in block[1:])
            if has_content:
                cleaned_lines.append(str(index_counter))
                cleaned_lines.append(block[0])  # 时间戳
                for c in block[1:]:
                    if c.strip():  # 只添加非空内容
                        cleaned_lines.append(c)
                cleaned_lines.append("")  # 添加空行

        # 写到 srt
        with open(srt_file_path, 'w', encoding='utf-8') as sf:
            for cl in cleaned_lines:
                sf.write(cl + '\n')

        logger.warning(f"已将 {vtt_file_path} 转成 {srt_file_path}")
        return True
    except Exception as e:
        logger.error(f"convert_vtt_to_srt error: {e}")
        return False

def convert_srt_to_txt(srt_file_path, txt_file_path):
    """将 .srt 文件转换为纯文本 - 原逻辑保持不变"""
    try:
        if not os.path.exists(srt_file_path):
            logger.error(f"转换失败，未找到 .srt 文件: {srt_file_path}")
            return

        with open(srt_file_path, 'r', encoding='utf-8') as srt_file:
            lines = srt_file.readlines()

        cleaned_lines = []
        for line in lines:
            # 跳过字幕序号和时间戳，只保留字幕文本
            if line.strip().isdigit() or '-->' in line:
                continue
            clean_line = re.sub(r"<[^>]+>", "", line).strip()  # 删除 HTML 标签
            if clean_line:  # 跳过空行
                cleaned_lines.append(clean_line)

        with open(txt_file_path, 'w', encoding='utf-8') as txt_file:
            txt_file.write(' '.join(cleaned_lines))

        logger.warning(f"已将 {srt_file_path} 转换为纯文字: {txt_file_path}")
    except Exception as e:
        logger.error(f"转换 .srt 为 .txt 时出错: {e}")

def download_chinese_subtitle_with_retry(video_url, channel_name, sanitized_title_full, retries=3):
    """
    尝试下载中文字幕 - 基于原逻辑，检测429错误
    """
    for attempt in range(retries):
        try:
            logger.warning(f"尝试下载中文字幕，第 {attempt + 1}/{retries} 次...")
            
            # 构建输出路径 - 原逻辑保持不变
            vtt_filename_template = f'【{channel_name}】{sanitized_title_full}.%(ext)s'
            vtt_file_path = os.path.join(VTT_HAN_FOLDER, vtt_filename_template)
            srt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.srt'
            srt_file_path = os.path.join(SRT_HAN_FOLDER, srt_filename)

            # 如果SRT文件已存在且大小合适，直接返回成功 - 原逻辑保持不变
            if check_subtitle_size(srt_file_path):
                logger.warning(f"中文字幕文件已存在: {srt_file_path}")
                return True, None

            # 构建下载命令 - 原逻辑保持不变
            command = [
                'yt-dlp',
                '--cookies-from-browser', 'firefox',
            ]
            if USE_PROXY:
                command += ['--proxy', PROXY_URL]
            command += [
                '--write-auto-sub',
                '--skip-download',
                '--sub-lang', 'zh-Hans',  # 中文字幕
                '--sub-format', 'vtt',
                '--socket-timeout', '60',
                '-o', vtt_file_path,
                video_url
            ]

            # 执行下载 - 原逻辑保持不变
            result = subprocess.run(command, capture_output=True, text=True)
            
            # 检查是否是429错误
            if result.returncode != 0:
                error_output = result.stderr
                if "429" in error_output or "Too Many Requests" in error_output:
                    logger.warning(f"第 {attempt + 1} 次遇到429错误")
                    if attempt < retries - 1:
                        wait_time = (attempt + 1) * 10  # 递增等待时间
                        logger.warning(f"等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        logger.warning("中文字幕下载失败，准备启动降级策略")
                        return False, "429_ERROR"
                else:
                    logger.error(f"中文字幕下载失败: {error_output}")
                    return False, "OTHER_ERROR"

            # 检查VTT文件 - 原逻辑保持不变
            expected_vtt_path = vtt_file_path.replace('%(ext)s', 'zh-Hans.vtt')
            if not check_subtitle_size(expected_vtt_path):
                logger.warning("中文VTT文件未生成或过小，继续重试...")
                continue

            # 转换为SRT格式 - 使用改进的转换函数
            if not convert_vtt_to_srt_improved(expected_vtt_path, srt_file_path, max_length=26):
                logger.warning("VTT转SRT失败，继续重试...")
                continue
            
            # 最终检查SRT文件 - 原逻辑保持不变
            if check_subtitle_size(srt_file_path):
                logger.warning("中文字幕下载成功")
                return True, None
                
        except Exception as e:
            logger.error(f"下载中文字幕时出错: {e}")
            
    return False, "RETRY_EXHAUSTED"

def download_english_subtitle_and_translate(video_url, channel_name, sanitized_title_full):
    """
    降级策略：下载英文字幕并翻译为中文 - 基于原逻辑
    """
    try:
        logger.warning("=== 启动降级策略：下载英文字幕并翻译为中文 ===")
        
        # 构建英文字幕输出路径
        vtt_filename_template = f'【{channel_name}】{sanitized_title_full}.%(ext)s'
        vtt_file_path = os.path.join(VTT_HAN_FOLDER, vtt_filename_template)
        
        # 最终中文字幕路径
        srt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.srt'
        srt_file_path = os.path.join(SRT_HAN_FOLDER, srt_filename)

        # 步骤1: 下载英文字幕 - 基于原逻辑，只改语言
        command = [
            'yt-dlp',
            '--cookies-from-browser', 'firefox',
        ]
        if USE_PROXY:
            command += ['--proxy', PROXY_URL]
        command += [
            '--write-auto-sub',
            '--skip-download',
            '--sub-lang', 'en',  # 英文字幕
            '--sub-format', 'vtt',
            '--socket-timeout', '60',
            '-o', vtt_file_path,
            video_url
        ]

        logger.warning(f"步骤1: 下载英文字幕")
        
        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode != 0:
            logger.error(f"英文字幕下载失败: {result.stderr}")
            return False

        # 检查英文VTT文件
        english_vtt_path = vtt_file_path.replace('%(ext)s', 'en.vtt')
        if not check_subtitle_size(english_vtt_path):
            logger.error(f"英文字幕文件未生成或文件过小: {english_vtt_path}")
            return False

        logger.warning(f"英文字幕下载成功: {english_vtt_path}")

        # 步骤2: 转换英文VTT为英文SRT - 使用改进的转换函数
        english_srt_path = english_vtt_path.replace('.en.vtt', '.en.srt')
        logger.warning("步骤2: 转换英文VTT为SRT...")
        if not convert_vtt_to_srt_improved(english_vtt_path, english_srt_path, max_length=26):
            logger.error("英文VTT转SRT失败")
            return False

        if not check_subtitle_size(english_srt_path):
            logger.error("英文SRT转换失败")
            return False

        # 步骤3: 翻译英文SRT为中文SRT
        logger.warning("步骤3: 翻译英文SRT为中文SRT...")
        if not translate_srt_file(english_srt_path, srt_file_path):
            logger.error("SRT翻译失败")
            return False

        # 步骤4: 转换为TXT - 原逻辑保持不变
        txt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.txt'
        txt_file_path = os.path.join(VTT_HAN_FOLDER, txt_filename)
        logger.warning("步骤4: 转换SRT为TXT...")
        convert_srt_to_txt(srt_file_path, txt_file_path)

        # 清理临时文件
        try:
            if os.path.exists(english_vtt_path):
                os.remove(english_vtt_path)
            if os.path.exists(english_srt_path):
                os.remove(english_srt_path)
            logger.warning("已清理临时英文字幕文件")
        except Exception as e:
            logger.warning(f"清理临时文件时出错: {e}")

        # 最终检查
        if check_subtitle_size(srt_file_path):
            logger.warning("=== 降级策略成功：英文字幕翻译完成 ===")
            return True
        else:
            logger.error("降级策略失败：最终文件检查不通过")
            return False

    except Exception as e:
        logger.error(f"降级策略执行时出错: {e}")
        return False

def translate_srt_file(english_srt_path, chinese_srt_path):
    """
    翻译英文SRT文件为中文SRT文件
    """
    try:
        if not os.path.exists(english_srt_path):
            logger.error(f"英文SRT文件不存在: {english_srt_path}")
            return False

        with open(english_srt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        translated_lines = []

        for line in lines:
            line = line.rstrip('\n\r')

            # 保留序号、时间戳和空行
            if (line.strip().isdigit() or
                '-->' in line or
                line.strip() == ''):
                translated_lines.append(line)
            else:
                # 翻译字幕内容行
                try:
                    clean_text = re.sub(r'<[^>]+>', '', line)
                    if clean_text.strip():
                        translated_text = translator.translate(clean_text)
                        translated_lines.append(translated_text)
                        logger.warning(f"翻译: '{clean_text}' -> '{translated_text}'")
                        time.sleep(0.2)  # 避免API限制
                    else:
                        translated_lines.append(line)
                except Exception as e:
                    logger.warning(f"翻译行失败，保留原文: {line} (错误: {e})")
                    translated_lines.append(line)

        # 保存翻译后的中文SRT文件
        with open(chinese_srt_path, 'w', encoding='utf-8') as f:
            for line in translated_lines:
                f.write(line + '\n')

        logger.warning(f"SRT翻译完成: {english_srt_path} -> {chinese_srt_path}")
        return True

    except Exception as e:
        logger.error(f"翻译SRT文件时出错: {e}")
        return False

def process_subtitle_with_fallback(video_url, channel_name, sanitized_title_full):
    """
    主处理函数：先尝试中文字幕，失败后使用英文字幕翻译降级策略
    完全基于原逻辑，只是增加了降级策略
    """
    logger.warning("开始字幕处理 - 带降级策略")

    # 步骤1: 尝试下载中文字幕（最多重试3次）
    success, error_type = download_chinese_subtitle_with_retry(video_url, channel_name, sanitized_title_full, retries=3)

    if success:
        logger.warning("中文字幕下载成功！")
        # 转换为TXT - 原逻辑保持不变
        srt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.srt'
        srt_file_path = os.path.join(SRT_HAN_FOLDER, srt_filename)
        txt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.txt'
        txt_file_path = os.path.join(VTT_HAN_FOLDER, txt_filename)
        convert_srt_to_txt(srt_file_path, txt_file_path)
        return True

    # 步骤2: 中文字幕下载失败，启动降级策略
    if error_type == "429_ERROR":
        logger.warning("检测到429错误，启动降级策略...")
    else:
        logger.warning("中文字幕下载失败，启动降级策略...")

    return download_english_subtitle_and_translate(video_url, channel_name, sanitized_title_full)

def check_chinese_subtitle_content(subtitle_file_path, min_chinese_chars=100):
    """
    检查字幕文件中是否包含足够数量的中文字符 - 原逻辑保持不变
    """
    try:
        if not os.path.exists(subtitle_file_path):
            logger.warning(f"字幕文件不存在: {subtitle_file_path}")
            return False

        # 读取字幕文件内容
        with open(subtitle_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 使用正则表达式匹配所有中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', content)
        chinese_count = len(chinese_chars)

        logger.warning(f"字幕文件 {subtitle_file_path} 中包含 {chinese_count} 个中文字符")

        if chinese_count >= min_chinese_chars:
            return True
        else:
            logger.warning(f"字幕文件中的中文字符数量不足: {chinese_count}/{min_chinese_chars}")
            return False
    except Exception as e:
        logger.error(f"检查字幕中文内容时出错: {e}")
        return False
