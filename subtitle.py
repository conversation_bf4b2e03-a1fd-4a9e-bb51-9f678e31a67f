import os
import os
import re
import time
import subprocess
from config import logger, USE_PROXY, PROXY_URL, VTT_HAN_FOLDER, SRT_HAN_FOLDER
from utils import sanitize_filename

# 导入谷歌翻译器（用于降级策略）
try:
    from deep_translator import GoogleTranslator
    translator = GoogleTranslator(source='en', target='zh-CN')
    TRANSLATOR_AVAILABLE = True
    logger.warning("谷歌翻译器初始化成功 - 降级策略可用")
except ImportError:
    TRANSLATOR_AVAILABLE = False
    logger.warning("谷歌翻译器不可用 - 降级策略禁用")

def check_subtitle_size(file_path, min_size_kb=1):
    """
    检查字幕文件是否存在且大小是否符合要求
    
    参数:
    - file_path: 字幕文件路径
    - min_size_kb: 最小文件大小（KB）
    
    返回:
    - bool: 如果文件存在且大小符合要求返回True，否则返回False
    """
    try:
        if not os.path.exists(file_path):
            logger.warning(f"字幕文件不存在: {file_path}")
            return False
            
        file_size_kb = os.path.getsize(file_path) / 1024  # 转换为KB
        if file_size_kb < min_size_kb:
            logger.warning(f"字幕文件大小不足 {min_size_kb}KB: {file_path} (当前大小: {file_size_kb:.2f}KB)")
            return False
            
        logger.warning(f"字幕文件检查通过: {file_path} (大小: {file_size_kb:.2f}KB)")
        return True
    except Exception as e:
        logger.error(f"检查字幕文件时出错: {e}")
        return False

def download_chinese_subtitle_with_retry(video_url, channel_name, sanitized_title_full, retries=3):
    """
    尝试下载中文字幕，检测429错误

    返回:
    - tuple: (是否成功, 错误类型)
    """
    for attempt in range(retries):
        try:
            logger.warning(f"尝试下载中文字幕，第 {attempt + 1}/{retries} 次...")

            # 构建输出路径
            vtt_filename_template = f'【{channel_name}】{sanitized_title_full}.%(ext)s'
            vtt_file_path = os.path.join(VTT_HAN_FOLDER, vtt_filename_template)
            srt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.srt'
            srt_file_path = os.path.join(SRT_HAN_FOLDER, srt_filename)

            # 如果SRT文件已存在且大小合适，直接返回成功
            if check_subtitle_size(srt_file_path):
                logger.warning(f"中文字幕文件已存在: {srt_file_path}")
                return True, None

            # 构建下载命令
            command = [
                'yt-dlp',
                '--cookies-from-browser', 'firefox',
            ]
            if USE_PROXY:
                command += ['--proxy', PROXY_URL]
            command += [
                '--write-auto-sub',
                '--skip-download',
                '--sub-lang', 'zh-Hans',
                '--sub-format', 'vtt',
                '--socket-timeout', '60',
                '-o', vtt_file_path,
                video_url
            ]

            # 执行下载
            result = subprocess.run(command, capture_output=True, text=True)

            # 检查是否是429错误
            if result.returncode != 0:
                error_output = result.stderr
                if "429" in error_output or "Too Many Requests" in error_output:
                    logger.warning(f"第 {attempt + 1} 次遇到429错误")
                    if attempt < retries - 1:
                        wait_time = (attempt + 1) * 10  # 递增等待时间
                        logger.warning(f"等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        logger.warning("中文字幕下载失败，准备启动降级策略")
                        return False, "429_ERROR"
                else:
                    logger.error(f"中文字幕下载失败: {error_output}")
                    return False, "OTHER_ERROR"

            # 检查VTT文件
            expected_vtt_path = vtt_file_path.replace('%(ext)s', 'zh-Hans.vtt')
            if not check_subtitle_size(expected_vtt_path):
                logger.warning("中文VTT文件未生成或过小，继续重试...")
                continue

            # 转换为SRT格式
            convert_vtt_to_srt(expected_vtt_path, srt_file_path, max_length=26)

            # 检查SRT文件是否存在（不检查大小，因为VTT已经验证过了）
            if os.path.exists(srt_file_path):
                logger.warning("中文字幕下载成功")
                return True, None

        except Exception as e:
            logger.error(f"下载中文字幕时出错: {e}")

    return False, "RETRY_EXHAUSTED"

def download_subtitle(video_url, channel_name, sanitized_title_full, retries=3, delay=10):
    """
    原有的下载字幕函数，保持兼容性

    返回:
    - tuple: (是否成功, 字幕文件路径)
    """
    success, error_type = download_chinese_subtitle_with_retry(video_url, channel_name, sanitized_title_full, retries)
    if success:
        srt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.srt'
        srt_file_path = os.path.join(SRT_HAN_FOLDER, srt_filename)
        return True, srt_file_path
    else:
        return False, None

def process_subtitles(video_url, channel_name, sanitized_title_full):
    """下载并处理中文字幕，返回是否成功"""
    for attempt in range(3):  # 最多尝试3次
        try:
            success, subtitle_path = download_subtitle(video_url, channel_name, sanitized_title_full)
            if success:
                return True
            logger.warning(f"第 {attempt + 1} 次尝试下载字幕失败，等待10秒后重试...")
            time.sleep(10)
        except Exception as e:
            logger.error(f"处理字幕时出错: {e}")
    return False

def process_chinese_subtitle(video_url, channel_name, sanitized_title_full, retries=3, delay=10):
    """主处理函数：下载并转换中文字幕"""
    for attempt in range(retries):
        try:
            logger.warning(f"下载中文字幕，尝试第 {attempt + 1} 次...")

            # 构建完整的输出路径
            vtt_filename_template = f'【{channel_name}】{sanitized_title_full}.%(ext)s'
            vtt_file_path = os.path.join(VTT_HAN_FOLDER, vtt_filename_template)

            # 生成 .srt 和 .txt 文件路径
            srt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.srt'
            srt_file_path = os.path.join(SRT_HAN_FOLDER, srt_filename)

            txt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.txt'
            txt_file_path = os.path.join(VTT_HAN_FOLDER, txt_filename)

            # 如果SRT文件已存在，则跳过下载
            if os.path.exists(srt_file_path):
                logger.warning(f"中文字幕文件 {srt_file_path} 已存在，跳过下载。")
                return True

            logger.warning("开始下载简体中文 (zh-Hans) 字幕...")

            # 构建下载命令
            command = [
                'yt-dlp',
                '--cookies-from-browser', 'firefox',
            ]
            if USE_PROXY:
                command += ['--proxy', PROXY_URL]
            command += [
                '--write-auto-sub',
                '--skip-download',
                '--sub-lang', 'zh-Hans',         # 中文字幕
                '--sub-format', 'vtt',           # VTT
                '--socket-timeout', '60',
                '-o', vtt_file_path,
                video_url
            ]

            logger.warning(f"执行字幕下载命令: {' '.join(command)}")
            result = subprocess.run(command, capture_output=True, text=True)

            # 打印 yt-dlp 的标准输出和错误输出
            logger.warning(f"yt-dlp 标准输出:\n{result.stdout}")
            logger.warning(f"yt-dlp 错误输出:\n{result.stderr}")

            if result.returncode == 0:
                logger.warning("中文字幕下载成功。")

                # 确认 .vtt 文件存在且大小合理
                expected_vtt_path = vtt_file_path.replace('%(ext)s', 'zh-Hans.vtt')
                if os.path.exists(expected_vtt_path) and os.path.getsize(expected_vtt_path) > 100:
                    logger.warning(f"中文字幕文件已成功下载: {expected_vtt_path}")
                    # 转换 vtt 到 srt
                    convert_vtt_to_srt(expected_vtt_path, srt_file_path, max_length=26)
                    # 转换 .srt 为 .txt
                    convert_srt_to_txt(srt_file_path, txt_file_path)
                    return True
                else:
                    logger.error(f"中文字幕文件未生成或文件过小: {expected_vtt_path}")
            else:
                logger.error(f"字幕下载失败，命令执行返回错误码: {result.returncode}")
        except subprocess.CalledProcessError as e:
            logger.error(f"下载简体中文字幕时命令执行失败（尝试 {attempt + 1}/{retries}）：{e}")
            logger.error(f"错误输出: {e.stderr}")
        except Exception as e:
            logger.error(f"下载简体中文字幕时发生未知错误（尝试 {attempt + 1}/{retries}）：{e}")

        if attempt < retries - 1:
            logger.warning(f"等待 {delay} 秒后重试下载字幕...")
            time.sleep(delay)

    logger.error("所有字幕下载尝试均失败。")
    return False

def should_skip_line(line):
    """
    检查是否需要跳过当前行（无用信息）。
    """
    if (
        not line  # 空行
        or line.startswith("WEBVTT")  # VTT 头部
        or line.startswith("Kind:")  # 字幕类型
        or line.startswith("Language:")  # 语言信息
    ):
        return True
    return False

def convert_vtt_to_srt(vtt_file_path, srt_file_path, max_length=50):
    """
    将 .vtt 转成标准 .srt，同时处理长字幕行:
      - 识别时间戳 "00:01:00.000 --> 00:01:05.000"
      - 移除vtt头、空行
      - 将行长度超过max_length就拆分
    """
    try:
        if not os.path.exists(vtt_file_path):
            logger.error(f"VTT 不存在: {vtt_file_path}")
            return

        with open(vtt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        cleaned_lines = []
        block = []
        index_counter = 1
        last_line_text = None

        for line in lines:
            line = line.strip()

            # 跳过头部 "WEBVTT"等
            if should_skip_line(line):
                continue

            # 如果这是时间戳行
            if '-->' in line:
                # 先把上一个block写入
                if block:
                    cleaned_lines.append(str(index_counter))
                    cleaned_lines.append(block[0])  # 时间戳
                    for c in block[1:]:
                        cleaned_lines.append(c)
                    cleaned_lines.append("")  # 添加空行
                    index_counter += 1
                    block = []  # 清空block

                # 修正vtt->srt时间戳
                line = line.replace('.', ',')  # 00:00:01.000 -> 00:00:01,000
                # 移除多余的 align: start position:0% 之类
                line = re.sub(r'\s*align:\w+|\s*position:\d+%', '', line).strip()
                block.append(line)
            else:
                # 非时间戳行(字幕内容)
                text = re.sub(r'<[^>]+>', '', line)  # 去掉HTML标签
                if text and text != last_line_text:
                    # 处理长行
                    splitted = split_long_lines_to_time_blocks(text, max_length=max_length)
                    block.extend(splitted)
                    last_line_text = text

        # 处理最后一个block
        if block:
            cleaned_lines.append(str(index_counter))
            cleaned_lines.append(block[0])  # 时间戳
            for c in block[1:]:
                cleaned_lines.append(c)
            cleaned_lines.append("")  # 添加空行

        # 写到 srt
        with open(srt_file_path, 'w', encoding='utf-8') as sf:
            for cl in cleaned_lines:
                sf.write(cl + '\n')

        logger.warning(f"已将 {vtt_file_path} 转成 {srt_file_path}")
    except Exception as e:
        logger.error(f"convert_vtt_to_srt error: {e}")

def split_long_lines(text, max_length=26):
    """
    将过长的字幕文本拆分为多行，每行不超过 max_length 个字符。
    对于中文，按字符数拆分；对于其他语言，按单词拆分。
    """
    # 判断是否包含中文字符
    if re.search(r'[\u4e00-\u9fff]', text):
        # 按字符数拆分
        lines = [text[i:i+max_length] for i in range(0, len(text), max_length)]
    else:
        # 按单词拆分
        words = text.split()
        lines = []
        current_line = []

        for word in words:
            if sum(len(w) for w in current_line) + len(word) + len(current_line) > max_length:
                lines.append(" ".join(current_line))
                current_line = [word]
            else:
                current_line.append(word)

        if current_line:
            lines.append(" ".join(current_line))
    return lines

def split_long_lines_to_time_blocks(text, max_length=50):
    """
    将长字幕拆分为多个时间块，每个块的字符数不超过 max_length。
    如果一行字幕超过最大长度，则将其拆分成多个部分，每个部分的字符数不超过 max_length。

    参数:
    - text: 长字幕文本
    - max_length: 每个时间块的最大字符数

    返回:
    - 返回字幕拆分后的多个部分，每个部分不超过 max_length 字符。
    """
    split_texts = []
    while len(text) > max_length:
        split_texts.append(text[:max_length])  # 分割字幕文本
        text = text[max_length:]  # 更新剩余文本
    split_texts.append(text)  # 添加剩余部分
    return split_texts

def convert_srt_to_txt(srt_file_path, txt_file_path):
    """
    将 .srt 文件转换为纯文本，保留字幕内容，去除时间戳和其他无用信息。
    """
    try:
        if not os.path.exists(srt_file_path):
            logger.error(f"转换失败，未找到 .srt 文件: {srt_file_path}")
            return

        with open(srt_file_path, 'r', encoding='utf-8') as srt_file:
            lines = srt_file.readlines()

        cleaned_lines = []
        for line in lines:
            # 跳过字幕序号和时间戳，只保留字幕文本
            if line.strip().isdigit() or '-->' in line:
                continue
            clean_line = re.sub(r"<[^>]+>", "", line).strip()  # 删除 HTML 标签
            if clean_line:  # 跳过空行
                cleaned_lines.append(clean_line)

        with open(txt_file_path, 'w', encoding='utf-8') as txt_file:
            txt_file.write(' '.join(cleaned_lines))

        logger.warning(f"已将 {srt_file_path} 转换为纯文字: {txt_file_path}")
    except Exception as e:
        logger.error(f"转换 .srt 为 .txt 时出错: {e}")

def check_chinese_subtitle_content(subtitle_file_path, min_chinese_chars=100):
    """
    检查字幕文件中是否包含足够数量的中文字符
    
    参数:
    - subtitle_file_path: 字幕文件路径
    - min_chinese_chars: 最少需要的中文字符数量
    
    返回:
    - bool: 如果中文字符数量符合要求返回True，否则返回False
    """
    try:
        if not os.path.exists(subtitle_file_path):
            logger.warning(f"字幕文件不存在: {subtitle_file_path}")
            return False
            
        # 读取字幕文件内容
        with open(subtitle_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 使用正则表达式匹配所有中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', content)
        chinese_count = len(chinese_chars)
        
        logger.warning(f"字幕文件 {subtitle_file_path} 中包含 {chinese_count} 个中文字符")
        
        if chinese_count >= min_chinese_chars:
            return True
        else:
            logger.warning(f"字幕文件中的中文字符数量不足: {chinese_count}/{min_chinese_chars}")
            return False
    except Exception as e:
        logger.error(f"检查字幕中文内容时出错: {e}")
        return False

def translate_srt_file(english_srt_path, chinese_srt_path):
    """
    翻译英文SRT文件为中文SRT文件
    """
    if not TRANSLATOR_AVAILABLE:
        logger.error("谷歌翻译器不可用，无法执行降级策略")
        return False

    try:
        if not os.path.exists(english_srt_path):
            logger.error(f"英文SRT文件不存在: {english_srt_path}")
            return False

        with open(english_srt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        translated_lines = []
        translate_count = 0

        for line in lines:
            line = line.rstrip('\n\r')

            # 保留序号、时间戳和空行
            if (line.strip().isdigit() or
                '-->' in line or
                line.strip() == ''):
                translated_lines.append(line)
            else:
                # 翻译字幕内容行
                try:
                    clean_text = re.sub(r'<[^>]+>', '', line)
                    if clean_text.strip():
                        translated_text = translator.translate(clean_text)
                        translated_lines.append(translated_text)
                        logger.warning(f"翻译: '{clean_text}' -> '{translated_text}'")
                        translate_count += 1
                        time.sleep(0.2)  # 避免API限制
                    else:
                        translated_lines.append(line)
                except Exception as e:
                    logger.warning(f"翻译行失败，保留原文: {line} (错误: {e})")
                    translated_lines.append(line)

        # 保存翻译后的中文SRT文件
        with open(chinese_srt_path, 'w', encoding='utf-8') as f:
            for line in translated_lines:
                f.write(line + '\n')

        logger.warning(f"SRT翻译完成: {english_srt_path} -> {chinese_srt_path}")
        logger.warning(f"翻译行数: {translate_count}")
        return True

    except Exception as e:
        logger.error(f"翻译SRT文件时出错: {e}")
        return False

def convert_vtt_to_srt_improved(vtt_file_path, srt_file_path, max_length=50):
    """
    改进的VTT转SRT - 修复空字幕块问题
    """
    try:
        if not os.path.exists(vtt_file_path):
            logger.error(f"VTT 不存在: {vtt_file_path}")
            return False

        with open(vtt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        cleaned_lines = []
        block = []
        index_counter = 1
        last_line_text = None

        for line in lines:
            line = line.strip()

            # 跳过头部 "WEBVTT"等
            if should_skip_line(line):
                continue

            # 如果这是时间戳行
            if '-->' in line:
                # 先把上一个block写入（如果有内容）
                if block and len(block) > 1:
                    # 检查是否有实际的字幕内容
                    content_lines = [content.strip() for content in block[1:] if content.strip()]
                    if content_lines:  # 有实际内容
                        cleaned_lines.append(str(index_counter))
                        cleaned_lines.append(block[0])  # 时间戳
                        for content in content_lines:
                            cleaned_lines.append(content)
                        cleaned_lines.append("")  # 添加空行
                        index_counter += 1

                block = []  # 清空block

                # 修正vtt->srt时间戳
                line = line.replace('.', ',')  # 00:00:01.000 -> 00:00:01,000
                # 移除多余的 align: start position:0% 之类
                line = re.sub(r'\s*align:\w+|\s*position:\d+%', '', line).strip()
                block.append(line)
            else:
                # 非时间戳行(字幕内容)
                # 去掉HTML标签和时间标记
                text = re.sub(r'<[^>]+>', '', line)  # 去掉HTML标签
                text = re.sub(r'<\d+:\d+:\d+\.\d+>', '', text)  # 去掉时间标记
                text = text.strip()

                if text and text != last_line_text:
                    # 处理长行
                    splitted = split_long_lines_to_time_blocks(text, max_length=max_length)
                    block.extend(splitted)
                    last_line_text = text

        # 处理最后一个block
        if block and len(block) > 1:
            content_lines = [content.strip() for content in block[1:] if content.strip()]
            if content_lines:
                cleaned_lines.append(str(index_counter))
                cleaned_lines.append(block[0])  # 时间戳
                for content in content_lines:
                    cleaned_lines.append(content)
                cleaned_lines.append("")  # 添加空行

        # 写到 srt
        with open(srt_file_path, 'w', encoding='utf-8') as sf:
            for cl in cleaned_lines:
                sf.write(cl + '\n')

        logger.warning(f"已将 {vtt_file_path} 转成 {srt_file_path}")
        return True
    except Exception as e:
        logger.error(f"convert_vtt_to_srt_improved error: {e}")
        return False