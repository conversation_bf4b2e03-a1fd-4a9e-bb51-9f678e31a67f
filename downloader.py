import os
import subprocess
import time
import random
from config import logger, USE_PROXY, PROXY_URL
from utils import save_downloaded_history, is_video_in_history, save_timestamp_info
from video_info import is_video_live
from translation import translate_title_with_retry
from subtitle import process_subtitles, check_subtitle_size, check_chinese_subtitle_content
from thumbnail_downloader import download_thumbnail

def download_video(video_id, video_title, channel_name, download_location, upload_date):
    """下载YouTube视频，严格按照字幕→封面→视频的顺序处理"""
    logger.warning(f"开始处理视频 - 上传日期: {upload_date}")

    # 检查是否已下载
    if is_video_in_history(video_id):
        logger.warning(f"视频已存在，跳过下载: [{channel_name}] {video_title}")
        return False

    # 检查是否在直播或预告中 (重要：放在最前面)
    video_url = f'https://www.youtube.com/watch?v={video_id}'
    logger.warning("检查视频是否是直播或直播预告...")
    
    live_status = is_video_live(video_url)
    if live_status:
        logger.warning(f"===检测到视频是直播或预告，完全跳过下载===: [{channel_name}] {video_title}")
        return False
    
    logger.warning("确认视频不是直播或预告，继续下载流程...")

    # 确定保存路径
    base_path = download_location
    os.makedirs(base_path, exist_ok=True)

    # 翻译标题
    translated_title = translate_title_with_retry(video_title)
    sanitized_title_full = translated_title
    sanitized_title_short = translated_title[:30]

    # 生成视频文件名
    filename = f'【{channel_name}】{sanitized_title_short}_{upload_date.strftime("%Y-%m-%d")}.mp4'
    output_path = os.path.join(base_path, filename)

    logger.warning(f"视频标题: {video_title}")
    logger.warning(f"翻译标题: {translated_title}")
    logger.warning(f"视频保存路径: {output_path}")

    # 步骤1: 下载字幕并严格验证
    logger.warning("第1步: 开始下载并验证中文字幕...")
    subtitle_success, subtitle_path = process_subtitles_with_validation(
        video_url, channel_name, sanitized_title_full
    )
    
    if not subtitle_success:
        logger.warning(f"无法获取到有效的中文字幕，跳过下载: {video_title}")
        return False
        
    logger.warning(f"中文字幕验证成功: {subtitle_path}")
    
    # 步骤2: 下载封面
    logger.warning("第2步: 开始下载视频封面...")
    thumbnail_path = download_thumbnail(video_id, channel_name, sanitized_title_short)
    
    if not thumbnail_path:
        logger.warning("封面下载失败，但仍继续下载视频...")
    else:
        logger.warning(f"封面下载成功: {thumbnail_path}")
    
    # 步骤3: 提取时间戳信息
    logger.warning("第3步: 提取视频时间戳信息...")
    timestamp_info = extract_video_timestamp(video_url)
    
    if timestamp_info:
        logger.warning(f"成功提取时间戳信息: {timestamp_info}")
        # 保存时间戳信息到文件
        timestamp_file = save_timestamp_info(
            video_id, channel_name, sanitized_title_full, timestamp_info
        )
        logger.warning(f"时间戳信息已保存到: {timestamp_file}")
    else:
        logger.warning("无法提取时间戳信息")
        
    # 步骤4: 下载视频
    logger.warning("第4步: 开始下载视频...")

    # 使用增强的下载函数，包含重试机制
    success = download_video_with_retry(video_url, output_path, max_retries=3)

    if success:
        logger.warning(f"成功下载视频：{video_title}")
        save_downloaded_history(video_id, channel_name, video_title,
                               subtitle_path, thumbnail_path, timestamp_info)
        return True
    else:
        logger.error(f"视频下载失败，已尝试所有重试机会: {video_title}")
        return False

def process_subtitles_with_validation(video_url, channel_name, sanitized_title_full, min_chinese_chars=100):
    """下载字幕并进行严格验证，确保包含足够的中文字符"""
    from subtitle import process_subtitles, check_subtitle_size, check_chinese_subtitle_content
    
    # 尝试下载字幕
    subtitle_success = process_subtitles(video_url, channel_name, sanitized_title_full)
    
    if not subtitle_success:
        logger.warning("字幕下载过程报告失败")
        return False, None
    
    # 确定字幕文件路径
    from config import SRT_HAN_FOLDER
    srt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.srt'
    srt_file_path = os.path.join(SRT_HAN_FOLDER, srt_filename)
    
    # 检查文件大小
    if not check_subtitle_size(srt_file_path, min_size_kb=1):
        logger.warning(f"字幕文件大小不足: {srt_file_path}")
        return False, None
    
    # 检查中文字符数量
    if not check_chinese_subtitle_content(srt_file_path, min_chinese_chars=min_chinese_chars):
        logger.warning(f"字幕文件中文字符数量不足: {srt_file_path}")
        return False, None
    
    logger.warning(f"字幕验证成功: 文件存在、大小合适且包含足够中文字符")
    return True, srt_file_path

def extract_video_timestamp(video_url):
    """提取视频的时间戳信息(时长等)"""
    try:
        command = [
            'yt-dlp',
            '--cookies-from-browser', 'firefox',
        ]
        if USE_PROXY:
            command += ['--proxy', PROXY_URL]
        command += [
            '--skip-download',
            '--print', '%(duration)s,%(upload_date)s,%(view_count)s',
            video_url
        ]
        
        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            # 解析输出: 持续时间(秒),上传日期,观看次数
            info_parts = result.stdout.strip().split(',')
            if len(info_parts) >= 3:
                duration_seconds = int(info_parts[0]) if info_parts[0].strip().isdigit() else 0
                upload_date = info_parts[1].strip()
                view_count = info_parts[2].strip()
                
                # 将秒转换为时:分:秒格式
                hours = duration_seconds // 3600
                minutes = (duration_seconds % 3600) // 60
                seconds = duration_seconds % 60
                duration_formatted = f"{hours:02}:{minutes:02}:{seconds:02}"
                
                return {
                    "duration_seconds": duration_seconds,
                    "duration_formatted": duration_formatted,
                    "upload_date": upload_date,
                    "view_count": view_count
                }
        
        logger.error(f"无法提取时间戳信息，yt-dlp输出: {result.stdout}")
        return None
    except Exception as e:
        logger.error(f"提取时间戳时出错: {e}")
        return None 