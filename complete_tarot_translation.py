#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的塔罗占卜视频字幕翻译流程
包含所有之前验证过的步骤
"""

import os
import re
import time
import requests
import json
import subprocess

class TarotSubtitleTranslator:
    def __init__(self, gemini_api_key, max_chars=3000):
        self.gemini_api_key = gemini_api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
        self.max_chars = max_chars
    
    def download_english_vtt(self, video_url, output_name):
        """步骤1: 下载英文VTT字幕"""
        print("步骤1: 下载英文VTT字幕")
        print("-" * 40)
        print(f"视频URL: {video_url}")
        print(f"输出名称: {output_name}")

        try:
            command = [
                'yt-dlp',
                '--cookies-from-browser', 'firefox',  # 使用Firefox的cookies
                '--write-auto-sub',
                '--skip-download',
                '--sub-lang', 'en',
                '--sub-format', 'vtt',
                '-o', f'{output_name}.%(ext)s',
                video_url
            ]

            print(f"执行命令: {' '.join(command)}")

            result = subprocess.run(command, capture_output=True, text=True, timeout=120)

            print(f"命令返回码: {result.returncode}")
            print(f"标准输出: {result.stdout}")
            if result.stderr:
                print(f"标准错误: {result.stderr}")

            vtt_file = f"{output_name}.en.vtt"
            print(f"检查文件: {vtt_file}")

            if os.path.exists(vtt_file):
                print(f"✅ 英文VTT下载成功: {vtt_file}")
                print(f"   文件大小: {os.path.getsize(vtt_file)} 字节")
                return vtt_file
            else:
                print(f"❌ VTT文件未生成: {vtt_file}")

                # 检查当前目录下是否有其他相关文件
                print("当前目录下的相关文件:")
                for file in os.listdir('.'):
                    if 'tarot' in file.lower() or output_name in file:
                        print(f"   找到: {file}")

                return None

        except subprocess.TimeoutExpired:
            print(f"❌ 下载超时 (120秒)")
            return None
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            return None
    
    def convert_vtt_to_srt_with_proper_dedup(self, vtt_file_path, srt_file_path):
        """步骤2: VTT转SRT，按照您之前的去重逻辑彻底去重"""
        print("\n步骤2: VTT转SRT (彻底去重)")
        print("-" * 40)

        try:
            with open(vtt_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            cleaned_lines = []
            block = []
            index_counter = 1
            last_line_text = None  # 您的去重关键变量
            duplicate_count = 0
            total_lines = 0

            for line in lines:
                line = line.strip()

                # 跳过头部信息
                if (not line or
                    line.startswith("WEBVTT") or
                    line.startswith("Kind:") or
                    line.startswith("Language:")):
                    continue

                # 如果这是时间戳行
                if '-->' in line:
                    # 先把上一个block写入（如果有内容）
                    if block and len(block) > 1:
                        content_lines = [content.strip() for content in block[1:] if content.strip()]
                        if content_lines:
                            cleaned_lines.append(str(index_counter))
                            cleaned_lines.append(block[0])  # 时间戳
                            for content in content_lines:
                                cleaned_lines.append(content)
                            cleaned_lines.append("")
                            index_counter += 1

                    block = []

                    # 修正vtt->srt时间戳格式
                    line = line.replace('.', ',')
                    line = re.sub(r'\s*align:\w+|\s*position:\d+%', '', line).strip()
                    block.append(line)

                else:
                    # 字幕内容行 - 这里是去重的关键
                    text = re.sub(r'<[^>]+>', '', line)  # 去掉HTML标签
                    text = re.sub(r'<\d+:\d+:\d+\.\d+>', '', text)  # 去掉时间标记
                    text = text.strip()
                    total_lines += 1

                    # 您的去重逻辑：只有当前行与上一行不同时才添加
                    if text and text != last_line_text:
                        block.append(text)
                        last_line_text = text
                    elif text == last_line_text and text:
                        duplicate_count += 1
                        if duplicate_count <= 10:  # 只显示前10个去重信息
                            print(f"   去重: 跳过重复 '{text[:30]}...'")

            # 处理最后一个block
            if block and len(block) > 1:
                content_lines = [content.strip() for content in block[1:] if content.strip()]
                if content_lines:
                    cleaned_lines.append(str(index_counter))
                    cleaned_lines.append(block[0])
                    for content in content_lines:
                        cleaned_lines.append(content)
                    cleaned_lines.append("")

            # 写入SRT文件
            with open(srt_file_path, 'w', encoding='utf-8') as sf:
                for cl in cleaned_lines:
                    sf.write(cl + '\n')

            print(f"✅ VTT转SRT完成，生成 {index_counter - 1} 个字幕块")
            print(f"   处理总行数: {total_lines}")
            print(f"   去重数量: {duplicate_count} 个重复字幕")
            print(f"   去重率: {duplicate_count/total_lines*100:.1f}%")
            print(f"   输出文件: {srt_file_path}")
            return True

        except Exception as e:
            print(f"❌ VTT转SRT出错: {e}")
            return False
    
    def check_file_has_punctuation(self, srt_content):
        """步骤3: 检查整个SRT文件是否包含标点符号"""
        print("\n步骤3: 检查文件标点符号")
        print("-" * 40)
        
        # 提取所有字幕内容
        subtitle_content = ""
        lines = srt_content.split('\n')
        
        for line in lines:
            line = line.strip()
            if (line.isdigit() or '-->' in line or not line):
                continue
            subtitle_content += line + " "
        
        # 检查标点符号
        punctuation_matches = re.findall(r'[.!?,:;]', subtitle_content)
        
        print(f"   字幕内容长度: {len(subtitle_content)} 字符")
        print(f"   标点符号数量: {len(punctuation_matches)}")
        
        if len(punctuation_matches) == 0:
            print("❌ 没有标点符号，字幕质量有问题")
            return False
        else:
            print(f"✅ 有 {len(punctuation_matches)} 个标点符号，可以翻译")
            return True
    
    def extract_complete_sentences(self, srt_content):
        """步骤4: 提取完整句子（VTT转SRT阶段已完成去重）"""
        print("\n步骤4: 提取完整句子")
        print("-" * 40)

        # 提取所有字幕内容
        subtitle_lines = []
        lines = srt_content.split('\n')

        for line in lines:
            line = line.strip()
            if (line.isdigit() or '-->' in line or not line):
                continue
            subtitle_lines.append(line)

        print(f"   字幕行数: {len(subtitle_lines)} (已在VTT转SRT阶段去重)")

        # 合并所有内容
        full_text = ' '.join(subtitle_lines)
        print(f"   合并文本长度: {len(full_text)} 字符")

        # 按句号分割句子
        sentences = re.split(r'(?<=[.!?])\s+', full_text.strip())
        sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 3]

        print(f"   分割出句子数: {len(sentences)}")

        return sentences
    
    def smart_batch_sentences(self, sentences):
        """步骤5: 智能分批算法"""
        print(f"\n步骤5: 智能分批 (限制: {self.max_chars} 字符)")
        print("-" * 40)
        
        batches = []
        current_batch = []
        current_chars = 0
        
        for sentence in sentences:
            # 计算加入这个句子后的总字符数（包括格式化）
            formatted_chars = len(f"{len(current_batch)+1}. {sentence}\n")
            
            # 检查加入这个句子后是否超过限制
            if current_chars + formatted_chars > self.max_chars:
                # 超过限制，保存当前批次
                if current_batch:
                    batches.append({
                        'sentences': current_batch.copy(),
                        'total_chars': current_chars,
                        'sentence_count': len(current_batch)
                    })
                    print(f"   批次 {len(batches)}: {len(current_batch)} 句, {current_chars} 字符")
                
                # 开始新批次
                current_batch = [sentence]
                current_chars = len(f"1. {sentence}\n")
            else:
                # 不超过限制，添加到当前批次
                current_batch.append(sentence)
                current_chars += formatted_chars
        
        # 添加最后一个批次
        if current_batch:
            batches.append({
                'sentences': current_batch.copy(),
                'total_chars': current_chars,
                'sentence_count': len(current_batch)
            })
            print(f"   批次 {len(batches)}: {len(current_batch)} 句, {current_chars} 字符")
        
        print(f"✅ 智能分批完成: 总共 {len(batches)} 批")
        return batches
    
    def translate_tarot_batch(self, batch_sentences, batch_num):
        """步骤6: 翻译塔罗占卜批次（提供上下文）"""
        print(f"\n翻译批次 {batch_num}: {len(batch_sentences)} 个句子")
        
        # 构建塔罗占卜专用prompt
        sentences_text = ""
        for i, sentence in enumerate(batch_sentences, 1):
            sentences_text += f"{i}. {sentence}\n"
        
        prompt = f"""请将以下英文字幕翻译成中文。这是塔罗占卜视频的字幕。

重要要求：
1. 只返回纯中文翻译，不要任何英文标签、注释或说明
2. 不要添加任何括号内的补充信息，如(Leo energy)、(Pisces energy)等
3. 不要添加字符统计或任何额外信息
4. 必须翻译所有内容，不能保留任何英文
5. 塔罗牌名称使用标准中文翻译
6. 保持占卜的神秘感和专业语气
7. 严格按照序号格式返回：序号. 中文翻译

英文原文：
{sentences_text}

请只返回纯中文翻译："""
        
        prompt_chars = len(prompt)
        print(f"   Prompt长度: {prompt_chars} 字符")
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.2,
                "topK": 1,
                "topP": 1,
                "maxOutputTokens": 4096,
            }
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            start_time = time.time()
            
            response = requests.post(
                f"{self.base_url}?key={self.gemini_api_key}",
                headers=headers,
                data=json.dumps(payload),
                timeout=120
            )
            
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text']
                    translations = self.parse_batch_result(content, len(batch_sentences))
                    
                    print(f"   ✅ 翻译成功! 耗时: {end_time - start_time:.2f}秒")
                    print(f"   翻译结果数: {len(translations)}")
                    
                    return translations
                else:
                    raise Exception("API返回格式错误")
            else:
                raise Exception(f"API错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
            return None
    
    def parse_batch_result(self, content, expected_count):
        """解析批量翻译结果并清理"""
        content = content.strip()

        # 按行分割
        lines = content.split('\n')
        translations = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 匹配格式：数字. 翻译内容
            match = re.match(r'^(\d+)\.\s*(.+)$', line)
            if match:
                index = int(match.group(1))
                translation = match.group(2).strip()

                # 清理翻译结果
                translation = self.clean_translation(translation)
                translations.append((index, translation))

        # 按序号排序并提取翻译内容
        translations.sort(key=lambda x: x[0])
        result = [t[1] for t in translations]

        return result

    def clean_translation(self, text):
        """清理翻译结果，移除英文标签和不需要的内容"""
        # 移除括号内的英文标签，如 (Leo energy), (Pisces energy) 等
        text = re.sub(r'\([^)]*[a-zA-Z][^)]*\)', '', text)

        # 移除字符统计，如 (15字符), (20字符) 等
        text = re.sub(r'\(\d+字符\)', '', text)

        # 移除其他英文标签
        text = re.sub(r'\([^)]*[a-zA-Z]+[^)]*\)', '', text)

        # 移除多余的空格
        text = re.sub(r'\s+', ' ', text).strip()

        # 移除开头和结尾的标点符号重复
        text = text.strip('.,，。')

        return text

    def check_for_english_content(self, srt_content):
        """检查SRT内容中是否还有英文"""
        lines = srt_content.split('\n')
        english_lines = []
        total_content_lines = 0

        for line in lines:
            line = line.strip()
            # 跳过序号和时间戳行
            if (line.isdigit() or '-->' in line or not line):
                continue

            total_content_lines += 1

            # 检查是否包含英文字母
            if re.search(r'[a-zA-Z]', line):
                english_lines.append(line)

        english_count = len(english_lines)
        if english_count == 0:
            return "✅ 100% 中文翻译，无英文内容"
        else:
            percentage = (total_content_lines - english_count) / total_content_lines * 100
            print(f"\n⚠️  发现 {english_count} 行包含英文内容:")
            for i, line in enumerate(english_lines[:5], 1):  # 只显示前5行
                print(f"   {i}. {line}")
            if len(english_lines) > 5:
                print(f"   ... 还有 {len(english_lines) - 5} 行")

            return f"⚠️  {percentage:.1f}% 中文翻译，{english_count} 行包含英文"

def test_complete_tarot_translation():
    """测试完整的塔罗占卜视频翻译流程"""
    print("完整塔罗占卜视频字幕翻译测试")
    print("=" * 60)
    
    # 配置
    video_url = "https://www.youtube.com/live/gHaWeqvTQjg?si=HmMsQ07ddMfATSLE"
    gemini_api_key = "AIzaSyBrEmZW9wqmyil7xPsbK2fgMb7LjII8NlQ"
    output_name = "tarot_reading"
    
    # 初始化翻译器
    translator = TarotSubtitleTranslator(gemini_api_key, max_chars=3000)
    
    # 步骤1: 下载英文VTT
    vtt_file = translator.download_english_vtt(video_url, output_name)
    if not vtt_file:
        return False
    
    # 步骤2: VTT转SRT (彻底去重)
    srt_file = f"{output_name}.en.srt"
    if not translator.convert_vtt_to_srt_with_proper_dedup(vtt_file, srt_file):
        return False
    
    # 步骤3: 读取SRT并检查标点符号
    with open(srt_file, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    if not translator.check_file_has_punctuation(srt_content):
        print("字幕质量检查失败")
        return False
    
    # 步骤4: 提取完整句子（已在VTT转SRT阶段去重）
    sentences = translator.extract_complete_sentences(srt_content)
    
    # 步骤5: 智能分批
    batches = translator.smart_batch_sentences(sentences)
    
    # 步骤6: 翻译所有批次（确保100%覆盖）
    print(f"\n步骤6: 开始翻译所有 {len(batches)} 批")
    print("=" * 60)

    all_translations = []
    success_batches = 0
    failed_batches = []

    for i, batch_info in enumerate(batches, 1):
        batch_sentences = batch_info['sentences']

        print(f"\n正在翻译批次 {i}/{len(batches)}...")
        translations = translator.translate_tarot_batch(batch_sentences, i)

        if translations and len(translations) == len(batch_sentences):
            all_translations.extend(translations)
            success_batches += 1
            print(f"   ✅ 批次 {i} 翻译成功: {len(translations)} 句")
        else:
            # 翻译失败，记录失败批次，但先用原文占位
            print(f"   ❌ 批次 {i} 翻译失败，使用原文")
            all_translations.extend(batch_sentences)
            failed_batches.append(i)

        # 避免API限制
        if i < len(batches):
            time.sleep(1)

    # 重试失败的批次
    if failed_batches:
        print(f"\n重试失败的批次: {failed_batches}")
        for batch_num in failed_batches:
            batch_info = batches[batch_num - 1]
            batch_sentences = batch_info['sentences']

            print(f"重试批次 {batch_num}...")
            translations = translator.translate_tarot_batch(batch_sentences, batch_num)

            if translations and len(translations) == len(batch_sentences):
                # 替换之前的原文
                start_idx = sum(len(batches[j]['sentences']) for j in range(batch_num - 1))
                end_idx = start_idx + len(batch_sentences)
                all_translations[start_idx:end_idx] = translations
                success_batches += 1
                print(f"   ✅ 批次 {batch_num} 重试成功")
            else:
                print(f"   ❌ 批次 {batch_num} 重试仍失败")

            time.sleep(2)

    print(f"\n" + "=" * 60)
    print(f"智能分批翻译完成:")
    print(f"  总批次数: {len(batches)}")
    print(f"  成功批次: {success_batches}")
    print(f"  失败批次: {len(failed_batches)}")
    print(f"  翻译句子数: {len(all_translations)}")
    print(f"  翻译成功率: {success_batches/len(batches)*100:.1f}%")

    # 生成真正的中文SRT字幕文件
    chinese_srt_file = f"{output_name}.zh-Hans.srt"

    # 读取原始SRT文件的结构
    with open(srt_file, 'r', encoding='utf-8') as f:
        original_srt_content = f.read()

    # 解析原始SRT的时间戳和结构
    original_blocks = []
    srt_blocks = original_srt_content.strip().split('\n\n')

    for block in srt_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            try:
                index = int(lines[0])
                timestamp = lines[1]
                content_lines = lines[2:]
                content = ' '.join(content_lines).strip()
                original_blocks.append({
                    'index': index,
                    'timestamp': timestamp,
                    'content': content
                })
            except ValueError:
                continue

    print(f"\n生成中文SRT字幕文件...")
    print(f"   原始字幕块数: {len(original_blocks)}")
    print(f"   翻译句子数: {len(all_translations)}")

    # 创建翻译映射表
    translation_map = {}
    original_sentences = []
    for batch_info in batches:
        original_sentences.extend(batch_info['sentences'])

    # 建立原文到译文的映射
    for i, (orig, trans) in enumerate(zip(original_sentences, all_translations)):
        translation_map[orig.strip().lower()] = trans

    print(f"   翻译映射表大小: {len(translation_map)}")

    # 将翻译结果映射回原始字幕块
    chinese_srt_lines = []
    translated_count = 0

    for block in original_blocks:
        block_content = block['content']
        block_content_clean = block_content.strip().lower()

        # 查找精确匹配的翻译
        found_translation = translation_map.get(block_content_clean)

        # 如果没有精确匹配，尝试部分匹配
        if found_translation is None:
            for orig_key, trans_value in translation_map.items():
                if (block_content_clean in orig_key or
                    orig_key in block_content_clean or
                    len(set(block_content_clean.split()) & set(orig_key.split())) > 2):
                    found_translation = trans_value
                    break

        # 如果还是没找到，使用原文
        if found_translation is None:
            found_translation = block_content
        else:
            translated_count += 1

        # 生成中文SRT块
        chinese_srt_lines.append(str(block['index']))
        chinese_srt_lines.append(block['timestamp'])
        chinese_srt_lines.append(found_translation)
        chinese_srt_lines.append('')

    print(f"   成功翻译的字幕块: {translated_count}/{len(original_blocks)}")
    print(f"   翻译覆盖率: {translated_count/len(original_blocks)*100:.1f}%")

    # 写入中文SRT文件
    chinese_srt_content = '\n'.join(chinese_srt_lines)
    with open(chinese_srt_file, 'w', encoding='utf-8') as f:
        f.write(chinese_srt_content)

    print(f"✅ 中文SRT字幕文件生成完成: {chinese_srt_file}")
    print(f"   文件大小: {os.path.getsize(chinese_srt_file)} 字节")

    # 检查是否还有英文内容
    english_check = translator.check_for_english_content(chinese_srt_content)

    # 显示前几行SRT内容
    print(f"\n中文SRT文件前20行:")
    lines = chinese_srt_content.split('\n')[:20]
    for i, line in enumerate(lines, 1):
        print(f"   {i:2d}: {line}")

    print(f"\n🎉 塔罗占卜视频翻译测试完成!")
    print(f"   中文字幕文件: {chinese_srt_file}")
    print(f"   翻译质量: {english_check}")
    print(f"   可直接用于视频播放器")
    
    return True

if __name__ == "__main__":
    test_complete_tarot_translation()
