#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕降级策略测试脚本
"""

import sys
import os

def test_fallback_strategy():
    """测试字幕降级策略"""
    print("=== 测试字幕降级策略 ===")
    
    try:
        from subtitle_fallback import process_chinese_subtitle_with_fallback
        
        # 测试视频信息
        video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        channel_name = "TestChannel"
        video_title = "Test Video Title"
        
        print(f"测试视频: {video_url}")
        print(f"频道名称: {channel_name}")
        print(f"视频标题: {video_title}")
        print("\n降级策略流程:")
        print("1. 先尝试下载中文字幕 (zh-Hans)")
        print("2. 如果遇到429错误，重试3次")
        print("3. 如果都失败，下载英文字幕并翻译为中文")
        print("-" * 50)
        
        # 执行降级策略
        success = process_chinese_subtitle_with_fallback(video_url, channel_name, video_title, retries=3)
        
        if success:
            print("✅ 字幕降级策略成功！")
            
            # 检查生成的文件
            from config import VTT_HAN_FOLDER, SRT_HAN_FOLDER
            
            srt_path = os.path.join(SRT_HAN_FOLDER, f"【{channel_name}】{video_title}.zh-Hans.srt")
            txt_path = os.path.join(VTT_HAN_FOLDER, f"【{channel_name}】{video_title}.zh-Hans.txt")
            
            files_to_check = [
                ("中文SRT文件", srt_path),
                ("中文TXT文件", txt_path)
            ]
            
            for file_desc, file_path in files_to_check:
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"✅ {file_desc}: {file_path} (大小: {file_size} 字节)")
                    
                    # 显示文件内容的前几行
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()[:300]  # 前300个字符
                        print(f"   内容预览: {content}...")
                        print("-" * 40)
                    except Exception as e:
                        print(f"   读取文件内容失败: {e}")
                else:
                    print(f"❌ {file_desc}: 文件不存在 - {file_path}")
            
            return True
        else:
            print("❌ 字幕降级策略失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def test_google_translate():
    """测试谷歌翻译功能"""
    print("\n=== 测试谷歌翻译功能 ===")
    
    try:
        from deep_translator import GoogleTranslator
        
        translator = GoogleTranslator(source='en', target='zh-CN')
        
        test_texts = [
            "Hello, how are you?",
            "This is a test subtitle.",
            "Welcome to our channel!",
            "Thank you for watching."
        ]
        
        for text in test_texts:
            translated = translator.translate(text)
            print(f"原文: {text}")
            print(f"译文: {translated}")
            print("-" * 40)
            
        return True
        
    except Exception as e:
        print(f"❌ 谷歌翻译测试失败: {e}")
        return False

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        import config
        print("✅ config 模块导入成功")
        
        import subtitle_fallback
        print("✅ subtitle_fallback 模块导入成功")
        
        import downloader_fallback
        print("✅ downloader_fallback 模块导入成功")
        
        import monitor_fallback
        print("✅ monitor_fallback 模块导入成功")
        
        from deep_translator import GoogleTranslator
        print("✅ GoogleTranslator 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n=== 测试配置 ===")
    
    try:
        from config import VTT_HAN_FOLDER, SRT_HAN_FOLDER, DOWNLOAD_PATHS
        
        print(f"✅ VTT文件夹: {VTT_HAN_FOLDER}")
        print(f"✅ SRT文件夹: {SRT_HAN_FOLDER}")
        print(f"✅ 下载路径: {DOWNLOAD_PATHS}")
        
        # 检查文件夹是否存在
        if os.path.exists(VTT_HAN_FOLDER):
            print(f"✅ VTT文件夹存在")
        else:
            print(f"⚠️  VTT文件夹不存在，将自动创建")
            
        if os.path.exists(SRT_HAN_FOLDER):
            print(f"✅ SRT文件夹存在")
        else:
            print(f"⚠️  SRT文件夹不存在，将自动创建")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("YouTube下载系统 - 字幕降级策略测试")
    print("=" * 60)
    print("降级策略说明:")
    print("1. 首先尝试下载中文字幕 (zh-Hans)")
    print("2. 如果遇到429错误，重试3次")
    print("3. 如果都失败，下载英文字幕并用谷歌翻译翻译为中文")
    print("4. 最终保存为标准的 .zh-Hans.srt 格式")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("配置检查", test_config),
        ("谷歌翻译", test_google_translate),
        ("字幕降级策略", test_fallback_strategy)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！字幕降级策略可以正常使用。")
        print("\n使用方法:")
        print("python main_fallback.py")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
