#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接询问Gemini API关于它自己的限制
"""

import requests
import json
import time

def ask_gemini_about_limits():
    """直接问Gemini API关于它的限制"""
    print("直接询问Gemini API关于它的限制")
    print("=" * 50)
    
    api_key = "AIzaSyBrEmZW9wqmyil7xPsbK2fgMb7LjII8NlQ"
    base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
    
    # 直接问API关于它的限制
    questions = [
        "你好！我想了解一下你的API限制。请告诉我：1. 单次输入的最大字符数限制是多少？2. 单次输出的最大字符数限制是多少？3. 有没有其他重要的限制我需要知道的？",
        
        "我想用你来翻译字幕文件，一个视频可能有100-200个英文句子需要翻译成中文。我可以一次性发送所有句子给你翻译吗？还是需要分批发送？",
        
        "如果我发送一个包含5000个英文字符的翻译请求给你，你能处理吗？10000个字符呢？20000个字符呢？",
        
        "你的输入token限制是多少？输出token限制是多少？1个中文字符大概等于多少个token？"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n问题 {i}: {question}")
        print("-" * 50)
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": question
                }]
            }],
            "generationConfig": {
                "temperature": 0.1,
                "topK": 1,
                "topP": 1,
                "maxOutputTokens": 2048,
            }
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(
                f"{base_url}?key={api_key}",
                headers=headers,
                data=json.dumps(payload),
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    answer = result['candidates'][0]['content']['parts'][0]['text']
                    print(f"Gemini回答: {answer}")
                else:
                    print("❌ API返回格式错误")
            else:
                print(f"❌ API错误: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        time.sleep(2)  # 避免API限制
    
    print(f"\n🎉 询问完成")

if __name__ == "__main__":
    ask_gemini_about_limits()
