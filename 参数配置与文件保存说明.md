# YouTube视频下载系统 - 参数配置与文件保存说明

本文档详细说明系统中的各种参数配置和文件保存位置，以便开发者和用户了解系统中数据的流转和存储方式。

## 1. 系统配置文件与参数

系统的主要配置位于`config.py`文件中，包含以下重要参数：

### 1.1 文件路径配置

```python
# config.py中的文件路径配置
CHANNELS_VIDEOS_FILE = 'D:/code/channels_videos_test.txt'  # 频道列表配置文件
HISTORY_VIDEOS_FILE = 'D:/code/downloaded_videos.txt'      # 下载历史记录文件
KEYWORDS_FILE = 'D:/code/keywords.txt'                     # 关键词过滤文件
```

### 1.2 时间与网络配置

```python
# 时间配置
CHECK_INTERVAL = 120  # 检查间隔时间（秒）

# 代理配置
USE_PROXY = False     # 设置为 True 如果需要使用代理
PROXY_URL = "http://127.0.0.1:7987"  # 代理服务器地址
```

### 1.3 存储目录配置

```python
# 字幕目录
VTT_HAN_FOLDER = "D:/ytb_python_download/vtt_han"    # 原始VTT格式字幕保存目录
SRT_HAN_FOLDER = "D:/ytb_python_download/srt_han"    # 转换后SRT格式字幕保存目录

# 下载路径配置
DOWNLOAD_PATHS = {
    "required": "D:/ytb_python_download/",          # 主要下载路径
    "alternative": "D:/ytb_python_download/alternative"  # 备用下载路径
}
```

### 1.4 API配置

```python
# 翻译API配置
TRANSLATE_API_URL = "https://translate.jayogo.com/v1/chat/completions"
TRANSLATE_API_KEY = "Bearer sk-mJtHjWKwIU10u3kiEfA3D22838A847079cBb66B88e2187A6"
```

## 2. 下载历史记录管理

下载历史记录的管理主要由`utils.py`文件中的相关函数处理：

### 2.1 下载历史记录文件格式

下载历史记录保存在`HISTORY_VIDEOS_FILE`(D:/code/downloaded_videos.txt)文件中，每行记录一个视频，格式如下：

```
{video_id} - [{channel_name}] {video_title} | 字幕: {subtitle_filename} | 封面: {thumbnail_filename} | 时长: {duration_formatted}
```

例如：
```
dQw4w9WgXcQ - [RickAstley] 永不放弃你 | 字幕: 【RickAstley】永不放弃你.zh-Hans.srt | 封面: 【RickAstley】永不放弃你.jpg | 时长: 00:03:33
```

### 2.2 加载下载历史记录

系统启动时，会调用`load_downloaded_history()`函数加载已下载的视频记录：
NoI
```python
# utils.py
def load_downloaded_history():
    """加载已下载的视频历史记录"""
    global downloaded_videos
    from config import HISTORY_VIDEOS_FILE
    
    if os.path.exists(HISTORY_VIDEOS_FILE):
        with open(HISTORY_VIDEOS_FILE, 'r', encoding='utf-8') as f:
            downloaded_videos = {line.split(' - ')[0].strip() for line in f}
        logger.warning(f"加载了 {len(downloaded_videos)} 个已下载的视频记录。")
    else:
        logger.error(f"未找到历史记录文件: {HISTORY_VIDEOS_FILE}")
        # 创建一个空的历史记录文件
        try:
            os.makedirs(os.path.dirname(HISTORY_VIDEOS_FILE), exist_ok=True)
            with open(HISTORY_VIDEOS_FILE, 'w', encoding='utf-8') as f:
                pass
            logger.warning(f"创建了空的历史记录文件: {HISTORY_VIDEOS_FILE}")
        except Exception as e:
            logger.error(f"创建历史记录文件失败: {e}")
```

加载后，视频ID被保存在内存中的`downloaded_videos`集合中，便于快速查询。

### 2.3 保存下载历史记录

当成功下载一个视频后，系统会调用`save_downloaded_history()`函数将记录追加到历史文件：

```python
# utils.py
def save_downloaded_history(video_id, channel_name, video_title, subtitle_path=None, thumbnail_path=None, timestamp_info=None):
    """保存已下载视频的记录，包含更多信息"""
    from config import HISTORY_VIDEOS_FILE
    
    # 基本记录
    record = f"{video_id} - [{channel_name}] {video_title}"
    
    # 添加额外信息
    if subtitle_path:
        record += f" | 字幕: {os.path.basename(subtitle_path)}"
    if thumbnail_path:
        record += f" | 封面: {os.path.basename(thumbnail_path)}"
    if timestamp_info and 'duration_formatted' in timestamp_info:
        record += f" | 时长: {timestamp_info['duration_formatted']}"
    
    with open(HISTORY_VIDEOS_FILE, 'a', encoding='utf-8') as f:
        f.write(record + "\n")
    downloaded_videos.add(video_id)
    logger.warning(f"记录已保存: {record}")
```

该函数同时将视频ID添加到内存中的集合，并在日志中记录保存操作。

### 2.4 检查视频是否已下载

下载前，系统会调用`is_video_in_history()`函数检查视频是否已下载过：

```python
# utils.py
def is_video_in_history(video_id):
    """检查视频是否已经下载过"""
    return video_id in downloaded_videos
```

此函数在`downloader.py`的`download_video()`函数中被调用，以避免重复下载。

## 3. 其他文件保存位置与格式

### 3.1 视频文件

视频文件保存在配置的下载路径中，文件名格式为：
```
【{channel_name}】{sanitized_title_short}_{upload_date}.mp4
```

相关代码位于`downloader.py`：
```python
# 生成视频文件名
filename = f'【{channel_name}】{sanitized_title_short}_{upload_date.strftime("%Y-%m-%d")}.mp4'
output_path = os.path.join(base_path, filename)
```

### 3.2 字幕文件

字幕文件分为两种格式：
- **VTT格式**：保存在`VTT_HAN_FOLDER`(D:/ytb_python_download/vtt_han)
- **SRT格式**：保存在`SRT_HAN_FOLDER`(D:/ytb_python_download/srt_han)

文件名格式为：
```
【{channel_name}】{sanitized_title_full}.zh-Hans.vtt
【{channel_name}】{sanitized_title_full}.zh-Hans.srt
```

### 3.3 封面图片

封面图片保存在`thumbnails`目录，文件名格式为：
```
【{channel_name}】{sanitized_title_short}.jpg
```

相关代码位于`thumbnail_downloader.py`。

### 3.4 时间戳信息

视频的时间戳信息（包括时长、上传日期、观看次数等）保存在`timestamps`目录，文件名格式为：
```
【{channel_name}】{video_title[:30]}_{video_id}.timestamp.txt
```

内容格式为：
```
视频ID: {video_id}
频道名称: {channel_name}
视频标题: {video_title}
时长: {timestamp_info.get('duration_formatted', '未知')}
上传日期: {timestamp_info.get('upload_date', '未知')}
观看次数: {timestamp_info.get('view_count', '未知')}
```

相关代码位于`utils.py`的`save_timestamp_info()`函数：
```python
def save_timestamp_info(video_id, channel_name, video_title, timestamp_info):
    """保存视频的时间戳信息到txt文件"""
    timestamp_dir = "D:/ytb_python_download/timestamps"
    os.makedirs(timestamp_dir, exist_ok=True)
    
    # 创建文件名
    filename = f"【{channel_name}】{video_title[:30]}_{video_id}.timestamp.txt"
    filepath = os.path.join(timestamp_dir, filename)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(f"视频ID: {video_id}\n")
        f.write(f"频道名称: {channel_name}\n")
        f.write(f"视频标题: {video_title}\n")
        f.write(f"时长: {timestamp_info.get('duration_formatted', '未知')}\n")
        f.write(f"上传日期: {timestamp_info.get('upload_date', '未知')}\n")
        f.write(f"观看次数: {timestamp_info.get('view_count', '未知')}\n")
    
    logger.warning(f"时间戳信息已保存到: {filepath}")
    return filepath
```

## 4. 重复检测与异步实现注意事项

### 4.1 视频重复检测

系统使用三层过滤机制检测重复视频：
1. **类型匹配**：短视频与短视频比较，长视频与长视频比较
2. **标题相似度**：使用fuzzywuzzy计算标题相似度
3. **内容特征**：比较文件大小和视频时长

相关代码位于`main.py`的`filter_similar_videos()`函数。

### 4.2 异步实现注意事项

在异步实现中，特别需要注意以下几点：

1. **并发安全**：当多个异步任务同时访问和修改`downloaded_videos`集合或写入历史文件时，需要添加锁或使用线程安全的数据结构确保数据一致性。

2. **文件写入同步**：多个异步任务同时写入同一个文件（如历史记录文件）时，需要确保原子性操作，避免文件内容损坏。

```python
# 异步安全的写入示例
import asyncio

# 创建一个文件写入锁
file_write_lock = asyncio.Lock()

async def save_downloaded_history_async(video_id, channel_name, video_title, subtitle_path=None, thumbnail_path=None, timestamp_info=None):
    """异步安全的保存下载历史记录"""
    global downloaded_videos
    from config import HISTORY_VIDEOS_FILE
    
    # 基本记录
    record = f"{video_id} - [{channel_name}] {video_title}"
    
    # 添加额外信息
    if subtitle_path:
        record += f" | 字幕: {os.path.basename(subtitle_path)}"
    if thumbnail_path:
        record += f" | 封面: {os.path.basename(thumbnail_path)}"
    if timestamp_info and 'duration_formatted' in timestamp_info:
        record += f" | 时长: {timestamp_info['duration_formatted']}"
    
    # 使用锁确保文件写入的原子性
    async with file_write_lock:
        with open(HISTORY_VIDEOS_FILE, 'a', encoding='utf-8') as f:
            f.write(record + "\n")
        downloaded_videos.add(video_id)
    
    logger.warning(f"记录已保存: {record}")
``` 