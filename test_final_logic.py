#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终逻辑：按句号组合翻译，放在起始块，翻译后检查41字符限制
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def parse_srt_blocks(srt_content):
    """解析SRT文件为字幕块"""
    blocks = []
    srt_blocks = srt_content.strip().split('\n\n')
    
    for block in srt_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            index = int(lines[0])
            timestamp = lines[1]
            content_lines = lines[2:]
            content = ' '.join(content_lines).strip()
            if content:
                blocks.append({
                    'index': index,
                    'timestamp': timestamp,
                    'content': content
                })
    
    return blocks

def group_by_complete_sentences(blocks):
    """按完整句子组合，记录起始块位置"""
    sentences = []
    i = 0
    
    while i < len(blocks):
        current_block = blocks[i]
        content = current_block['content'].strip()
        
        if not content:
            i += 1
            continue
            
        # 特殊标记单独处理
        if content in ['[Music]', '[Applause]', '[Laughter]']:
            sentences.append({
                'content': content,
                'start_block_index': i,
                'end_block_index': i,
                'blocks_used': [i]
            })
            i += 1
            continue
        
        # 开始组合句子
        sentence_content = content
        start_block_index = i
        blocks_used = [i]
        
        # 检查当前块是否以句号结尾
        if re.search(r'[.!?]$', content):
            sentences.append({
                'content': sentence_content,
                'start_block_index': start_block_index,
                'end_block_index': i,
                'blocks_used': blocks_used
            })
            i += 1
            continue
        
        # 向后查找句子结尾
        j = i + 1
        while j < len(blocks):
            next_block = blocks[j]
            next_content = next_block['content'].strip()
            
            if not next_content:
                j += 1
                continue
            
            sentence_content += ' ' + next_content
            blocks_used.append(j)
            
            if re.search(r'[.!?]$', next_content):
                sentences.append({
                    'content': sentence_content,
                    'start_block_index': start_block_index,
                    'end_block_index': j,
                    'blocks_used': blocks_used
                })
                i = j + 1
                break
            
            j += 1
        else:
            sentences.append({
                'content': sentence_content,
                'start_block_index': start_block_index,
                'end_block_index': j - 1,
                'blocks_used': blocks_used
            })
            i = j
    
    return sentences

def split_long_text(text, max_length=41):
    """将超长文本按41字符限制拆分"""
    if len(text) <= max_length:
        return [text]
    
    parts = []
    while len(text) > max_length:
        # 尝试在合适的位置断开
        split_pos = max_length
        
        # 向前查找合适的断点（标点符号或空格）
        for i in range(max_length - 1, max(0, max_length - 10), -1):
            if text[i] in '，。！？；：、 ':
                split_pos = i + 1
                break
        
        parts.append(text[:split_pos].strip())
        text = text[split_pos:].strip()
    
    if text:
        parts.append(text)
    
    return parts

def translate_and_assign_to_start_blocks(sentences, all_blocks):
    """翻译句子并分配到起始块，翻译后检查41字符限制"""
    result_blocks = []
    used_block_indices = set()
    
    for i, sentence in enumerate(sentences, 1):
        content = sentence['content'].strip()
        start_block_index = sentence['start_block_index']
        blocks_used = sentence['blocks_used']
        
        print(f"句子 {i}: '{content[:50]}{'...' if len(content) > 50 else ''}'")
        print(f"   起始块: {start_block_index + 1}, 使用块: {[idx + 1 for idx in blocks_used]}")
        
        # 检查是否需要翻译
        has_punctuation = bool(re.search(r'[.!?,:;]', content))
        
        if not has_punctuation and content not in ['[Music]', '[Applause]', '[Laughter]']:
            print(f"   ⚠️  无标点符号，跳过翻译")
            translated_content = content
        else:
            # 翻译完整句子
            try:
                if content in ['[Music]', '[Applause]', '[Laughter]']:
                    translation_map = {
                        '[Music]': '[音乐]',
                        '[Applause]': '[掌声]',
                        '[Laughter]': '[笑声]'
                    }
                    translated_content = translation_map.get(content, content)
                    print(f"   特殊标记: '{content}' → '{translated_content}'")
                else:
                    translated_content = translator.translate(content)
                    print(f"   翻译结果: '{translated_content}'")
                    time.sleep(0.3)
                
            except Exception as e:
                print(f"   ❌ 翻译失败: {e}")
                translated_content = content
        
        # 翻译后检查41字符限制
        print(f"   翻译长度: {len(translated_content)} 字符")
        if len(translated_content) > 41:
            print(f"   ⚠️  超过41字符限制，进行拆分")
            split_parts = split_long_text(translated_content, max_length=41)
            print(f"   拆分为 {len(split_parts)} 部分: {split_parts}")
        else:
            split_parts = [translated_content]
        
        # 将翻译结果分配到起始块和后续块
        start_block = all_blocks[start_block_index]
        
        # 第一部分放在起始块
        result_blocks.append({
            'index': start_block['index'],
            'timestamp': start_block['timestamp'],
            'content': split_parts[0]
        })
        used_block_indices.add(start_block_index)
        print(f"   块 [{start_block['index']}]: '{split_parts[0]}'")
        
        # 如果有多个部分，需要额外的块
        if len(split_parts) > 1:
            # 尝试使用被合并的块来放置额外的部分
            available_blocks = [idx for idx in blocks_used[1:] if idx not in used_block_indices]
            
            for part_idx, part in enumerate(split_parts[1:], 1):
                if part_idx - 1 < len(available_blocks):
                    # 使用可用的块
                    block_idx = available_blocks[part_idx - 1]
                    block = all_blocks[block_idx]
                    result_blocks.append({
                        'index': block['index'],
                        'timestamp': block['timestamp'],
                        'content': part
                    })
                    used_block_indices.add(block_idx)
                    print(f"   块 [{block['index']}]: '{part}'")
                else:
                    # 没有足够的块，创建新的块（使用最后一个块的时间戳）
                    last_block = all_blocks[blocks_used[-1]]
                    result_blocks.append({
                        'index': f"{last_block['index']}_extra_{part_idx}",
                        'timestamp': last_block['timestamp'],
                        'content': part
                    })
                    print(f"   额外块 [{last_block['index']}_extra_{part_idx}]: '{part}'")
        
        # 标记其他被使用的块为已使用（但不添加到结果中）
        for block_idx in blocks_used[1:]:
            used_block_indices.add(block_idx)
    
    # 添加未被使用的块（保持原文）
    for i, block in enumerate(all_blocks):
        if i not in used_block_indices:
            result_blocks.append({
                'index': block['index'],
                'timestamp': block['timestamp'],
                'content': block['content']
            })
            print(f"   保留原文块 [{block['index']}]: '{block['content']}'")
    
    # 按index排序
    result_blocks.sort(key=lambda x: int(str(x['index']).split('_')[0]))
    
    return result_blocks

def rebuild_srt_from_blocks(blocks):
    """从字幕块重建SRT"""
    srt_lines = []
    
    for i, block in enumerate(blocks, 1):
        srt_lines.append(str(i))  # 重新编号
        srt_lines.append(block['timestamp'])
        srt_lines.append(block['content'])
        srt_lines.append('')
    
    return '\n'.join(srt_lines)

def test_final_logic():
    """测试最终逻辑"""
    print("最终逻辑测试：按句号组合翻译，放在起始块，翻译后检查41字符限制")
    print("=" * 70)
    
    english_srt = "real_test_improved.en.srt"
    chinese_srt = "final_logic.zh-Hans.srt"
    
    if not os.path.exists(english_srt):
        print(f"❌ 英文SRT文件不存在: {english_srt}")
        return False
    
    print(f"✅ 找到英文SRT文件: {english_srt}")
    
    # 读取英文SRT
    with open(english_srt, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    # 解析为字幕块
    blocks = parse_srt_blocks(srt_content)
    print(f"解析出 {len(blocks)} 个有效字幕块")
    
    # 按完整句子组合
    sentences = group_by_complete_sentences(blocks)
    print(f"组合成 {len(sentences)} 个完整句子")
    
    # 只处理前3个句子进行测试
    test_sentences = sentences[:3]
    print(f"\n测试前 {len(test_sentences)} 个句子:")
    for i, sentence in enumerate(test_sentences, 1):
        print(f"  {i}. '{sentence['content'][:60]}{'...' if len(sentence['content']) > 60 else ''}'")
        print(f"     起始块: {sentence['start_block_index'] + 1}, 使用块: {[idx + 1 for idx in sentence['blocks_used']]}")
    
    # 翻译并分配
    print(f"\n开始翻译和分配...")
    result_blocks = translate_and_assign_to_start_blocks(test_sentences, blocks)
    
    # 重建SRT
    chinese_srt_content = rebuild_srt_from_blocks(result_blocks)
    
    # 保存
    with open(chinese_srt, 'w', encoding='utf-8') as f:
        f.write(chinese_srt_content)
    
    print(f"\n✅ 最终逻辑测试完成")
    print(f"   输出文件: {chinese_srt}")
    print(f"   生成了 {len(result_blocks)} 个字幕块")
    
    # 显示结果
    print("\n最终结果:")
    print("-" * 70)
    print(chinese_srt_content)
    print("-" * 70)
    
    # 检查字符限制
    print("\n字符限制检查:")
    for block in result_blocks:
        content_length = len(block['content'])
        status = "✅" if content_length <= 41 else "❌"
        print(f"   块 [{block['index']}]: {content_length} 字符 {status} '{block['content']}'")
    
    return True

if __name__ == "__main__":
    test_final_logic()
