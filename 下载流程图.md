# YouTube视频下载系统 - 下载流程图

## 整体系统流程

```
┌─────────────────┐
│                 │
│  启动 main.py   │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│  加载配置文件   │────▶│ 初始化日志系统  │
│  config.py      │     │                 │
│                 │     │                 │
└────────┬────────┘     └────────┬────────┘
         │                       │
         │                       ▼
         │              ┌─────────────────┐
         │              │                 │
         └──────────────│ 启动监控循环    │
                        │ monitor.py      │
                        │                 │
                        └────────┬────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │                 │
                        │ 定期检查频道    │◀──┐
                        │                 │   │
                        └────────┬────────┘   │
                                 │            │
                                 ▼            │
                        ┌─────────────────┐   │
                        │                 │   │
                        │ 处理视频列表    │   │
                        │                 │   │
                        └────────┬────────┘   │
                                 │            │
                                 ▼            │
                        ┌─────────────────┐   │
                        │                 │   │
                        │ 等待检查间隔    │───┘
                        │ CHECK_INTERVAL  │
                        │                 │
                        └─────────────────┘
```

## 视频监控流程

```
┌─────────────────┐
│                 │
│ monitor_video_  │
│ channels()      │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│ 加载下载历史    │────▶│ 加载关键词过滤  │
│ 和配置信息      │     │                 │
│                 │     │                 │
└────────┬────────┘     └────────┬────────┘
         │                       │
         │                       ▼
         │              ┌─────────────────┐
         └──────────────│ 加载频道列表    │
                        │                 │
                        └────────┬────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │                 │
               ┌────────│ 遍历每个频道    │
               │        │                 │
               │        └────────┬────────┘
               │                 │
               ▼                 ▼
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│ 获取频道最新    │────▶│ 过滤关键词      │
│ 视频列表        │     │                 │
│                 │     │                 │
└─────────────────┘     └────────┬────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │                 │
                        │ 下载符合条件    │
                        │ 的视频          │
                        │                 │
                        └─────────────────┘
```

## 视频下载流程

```
┌─────────────────┐
│                 │
│ download_video()│
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│                 │ No  │                 │
│ 检查是否已下载  │────▶│ 检查是否为直播  │
│                 │     │                 │
└────────┬────────┘     └────────┬────────┘
         │ Yes                   │
         ▼                       │ No
┌─────────────────┐              ▼
│                 │     ┌─────────────────┐
│ 返回False       │     │                 │
│ (跳过下载)      │     │ 翻译视频标题    │
│                 │     │                 │
└─────────────────┘     └────────┬────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │                 │
                        │ 1. 下载字幕     │◀──┐
                        │                 │   │
                        └────────┬────────┘   │
                                 │            │
                                 ▼            │
                        ┌─────────────────┐   │
                        │                 │   │
                        │ 2. 验证字幕质量 │───┘
                        │                 │   
                        └────────┬────────┘   
                                 │ 通过验证            
                                 ▼            
                        ┌─────────────────┐   
                        │                 │   
                        │ 3. 下载封面     │ 
                        │                 │   
                        └────────┬────────┘   
                                 │            
                                 ▼            
                        ┌─────────────────┐   
                        │                 │   
                        │ 4. 提取时间戳   │ 
                        │                 │   
                        └────────┬────────┘   
                                 │            
                                 ▼            
                        ┌─────────────────┐   
                        │                 │   
                        │ 5. 下载视频     │ 
                        │                 │   
                        └────────┬────────┘   
                                 │            
                                 ▼            
                        ┌─────────────────┐   
                        │                 │   │
                        │ 6. 保存历史记录 │───┘
                        │                 │
                        └─────────────────┘
```

## 字幕处理流程

```
┌─────────────────┐
│                 │
│ process_        │
│ subtitles()     │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│ 尝试下载VTT字幕 │────▶│ 检查是否下载    │
│                 │     │ 成功            │
└─────────────────┘     └────────┬────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │                 │ No
               ┌────────│ 字幕文件存在?   │────┐
               │        │                 │    │
               │        └────────┬────────┘    │
               │                 │ Yes         │
               ▼                 ▼             ▼
┌─────────────────┐     ┌─────────────────┐   ┌─────────────────┐
│                 │     │                 │   │                 │
│ 尝试使用本地    │────▶│ 转换为SRT格式   │   │ 返回False      │
│ 字幕文件        │     │                 │   │                 │
│                 │     │                 │   │                 │
└─────────────────┘     └────────┬────────┘   └─────────────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │                 │
                        │ 验证字幕质量:   │
                        │ 1. 文件大小     │
                        │ 2. 中文字符数   │
                        └────────┬────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │                 │
                        │ 返回验证结果    │
                        │                 │
                        └─────────────────┘
```

## 翻译系统流程

```
┌─────────────────┐
│                 │
│ translate_title_│
│ with_retry()    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ 尝试使用自定义  │
│ API翻译         │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │ No  │                 │ No  │                 │
│ 成功?           │────▶│ 重试次数未超过? │────▶│ 使用谷歌翻译    │
│                 │     │                 │     │ 作为备选         │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │ Yes                   │ Yes                   │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │                 │              │
         │              │ 等待后重试      │              │
         │              │                 │              │
         │              └────────┬────────┘              │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                      返回翻译结果                               │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 重复视频检测流程

```
┌─────────────────┐
│                 │
│ filter_similar_ │
│ videos()        │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│ 对于每个待处理视频与已处理视频进行三层比较:                     │
│                                                                 │
└────────────────────────────────┬──────────────────────────────┬─┘
                                 │                              │
┌────────────────────────────────▼──────────────────────────────▼─┐
│                                                                 │
│ 第一层: 类型匹配 (短视频只与短视频比较)                         │
│                                                                 │
└────────────────────────────────┬──────────────────────────────┬─┘
                                 │ 匹配                         │ 不匹配
                                 ▼                              │
┌─────────────────────────────────────────────────────────┐     │
│                                                         │     │
│ 第二层: 标题相似度比较 (使用fuzzywuzzy模糊匹配)         │     │
│                                                         │     │
└────────────────────────────────┬──────────────────────┬─┘     │
                                 │ 相似度>=阈值         │ 相似度<阈值
                                 ▼                      │       │
┌─────────────────────────────────────────────────┐     │       │
│                                                 │     │       │
│ 第三层: 内容特征比较 (文件大小、视频时长)       │     │       │
│                                                 │     │       │
└────────────────────────────────┬──────────────┬─┘     │       │
                                 │ 都相似       │ 至少一项不同  │
                                 ▼              │       │       │
┌─────────────────┐              │              │       │       │
│                 │              │              │       │       │
│ 判定为重复视频  │              │              │       │       │
│                 │              │              │       │       │
└────────┬────────┘              │              │       │       │
         │                       ▼              ▼       ▼       ▼
         │              ┌─────────────────────────────────────────┐
         │              │                                         │
         └──────────────│            判定为新视频                 │
                        │                                         │
                        └─────────────────────────────────────────┘
``` 