import os
import logging

# 获取当前脚本所在的绝对路径
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

# 配置日志
logging.basicConfig(
    level=logging.WARNING,               # 设置日志级别为 WARNING，显示 WARNING 及以上级别的日志
    format='%(message)s',                # 简化日志格式，仅显示消息内容
    handlers=[
        logging.FileHandler(os.path.join(CURRENT_DIR, "script.log"), encoding='utf-8'),  # 写入日志文件
        logging.StreamHandler()                                 # 同时输出到控制台
    ]
)

logger = logging.getLogger(__name__)

# 文件路径配置
CHANNELS_VIDEOS_FILE = os.path.join(CURRENT_DIR, 'channels_videos_test.txt')
HISTORY_VIDEOS_FILE = os.path.join(CURRENT_DIR, 'downloaded_videos.txt')
KEYWORDS_FILE = os.path.join(CURRENT_DIR, 'keywords.txt')

# 时间配置
CHECK_INTERVAL = 120  # 检查间隔时间（秒）

# 代理配置
USE_PROXY = False        # 设置为 True 如果需要使用代理
PROXY_URL = "http://127.0.0.1:7987"

# 字幕目录
VTT_HAN_FOLDER = "D:/ytb_python_download/vtt_han"
SRT_HAN_FOLDER = "D:/ytb_python_download/srt_han"
os.makedirs(VTT_HAN_FOLDER, exist_ok=True)
os.makedirs(SRT_HAN_FOLDER, exist_ok=True)

# 下载路径配置
DOWNLOAD_PATHS = {
    "required": "D:/ytb_python_download/",
    "alternative": "D:/ytb_python_download/alternative"
}

# 翻译API配置
TRANSLATE_API_URL = "https://translate.jayogo.com/v1/chat/completions"
TRANSLATE_API_KEY = "Bearer sk-mJtHjWKwIU10u3kiEfA3D22838A847079cBb66B88e2187A6"
