#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
英文字幕翻译功能测试脚本
"""

import sys
import os

def test_english_subtitle_download():
    """测试英文字幕下载和翻译功能"""
    print("=== 测试英文字幕下载和翻译 ===")
    
    try:
        from subtitle_english_translate import process_english_subtitle_with_translation
        
        # 测试视频信息
        video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        channel_name = "TestChannel"
        video_title = "Test Video Title"
        
        print(f"测试视频: {video_url}")
        print(f"频道名称: {channel_name}")
        print(f"视频标题: {video_title}")
        
        # 执行英文字幕下载和翻译
        success = process_english_subtitle_with_translation(video_url, channel_name, video_title)
        
        if success:
            print("✅ 英文字幕下载和翻译成功！")
            
            # 检查生成的文件
            from config import VTT_HAN_FOLDER, SRT_HAN_FOLDER
            
            chinese_vtt_path = os.path.join(VTT_HAN_FOLDER, f"【{channel_name}】{video_title}.zh-Hans.vtt")
            srt_path = os.path.join(SRT_HAN_FOLDER, f"【{channel_name}】{video_title}.zh-Hans.srt")
            txt_path = os.path.join(VTT_HAN_FOLDER, f"【{channel_name}】{video_title}.zh-Hans.txt")
            
            files_to_check = [
                ("中文VTT文件", chinese_vtt_path),
                ("中文SRT文件", srt_path),
                ("中文TXT文件", txt_path)
            ]
            
            for file_desc, file_path in files_to_check:
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"✅ {file_desc}: {file_path} (大小: {file_size} 字节)")
                    
                    # 显示文件内容的前几行
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()[:200]  # 前200个字符
                        print(f"   内容预览: {content}...")
                    except Exception as e:
                        print(f"   读取文件内容失败: {e}")
                else:
                    print(f"❌ {file_desc}: 文件不存在 - {file_path}")
            
            return True
        else:
            print("❌ 英文字幕下载和翻译失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def test_google_translate():
    """测试谷歌翻译功能"""
    print("\n=== 测试谷歌翻译功能 ===")
    
    try:
        from subtitle_english_translate import translate_subtitle_text
        
        test_texts = [
            "Hello, how are you?",
            "This is a test subtitle.",
            "Welcome to our channel!",
            "Thank you for watching."
        ]
        
        for text in test_texts:
            translated = translate_subtitle_text(text)
            print(f"原文: {text}")
            print(f"译文: {translated}")
            print("-" * 40)
            
        return True
        
    except Exception as e:
        print(f"❌ 谷歌翻译测试失败: {e}")
        return False

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        import config
        print("✅ config 模块导入成功")
        
        import subtitle_english_translate
        print("✅ subtitle_english_translate 模块导入成功")
        
        import downloader_english_translate
        print("✅ downloader_english_translate 模块导入成功")
        
        from deep_translator import GoogleTranslator
        print("✅ GoogleTranslator 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("英文字幕翻译功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("谷歌翻译", test_google_translate),
        ("英文字幕下载翻译", test_english_subtitle_download)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！英文字幕翻译功能可以正常使用。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
