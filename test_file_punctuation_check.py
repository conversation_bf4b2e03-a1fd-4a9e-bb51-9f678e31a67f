#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整个文件标点符号检查逻辑
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def check_file_has_punctuation(srt_content):
    """检查整个SRT文件是否包含标点符号"""
    print("=== 检查整个文件是否包含标点符号 ===")
    
    # 提取所有字幕内容（排除序号和时间戳）
    subtitle_content = ""
    lines = srt_content.split('\n')
    
    for line in lines:
        line = line.strip()
        # 跳过序号、时间戳和空行
        if (line.isdigit() or 
            '-->' in line or 
            not line):
            continue
        subtitle_content += line + " "
    
    print(f"提取的字幕内容: '{subtitle_content[:200]}{'...' if len(subtitle_content) > 200 else ''}'")
    print(f"总字符数: {len(subtitle_content)}")
    
    # 检查是否包含标点符号
    punctuation_pattern = r'[.!?,:;]'
    punctuation_matches = re.findall(punctuation_pattern, subtitle_content)
    
    print(f"找到的标点符号: {punctuation_matches}")
    print(f"标点符号数量: {len(punctuation_matches)}")
    
    if len(punctuation_matches) == 0:
        print("❌ 整个文件没有标点符号，字幕质量有问题")
        return False
    else:
        print(f"✅ 文件包含 {len(punctuation_matches)} 个标点符号，可以进行翻译")
        return True

def split_sentences_correctly(text):
    """正确分割句子"""
    # 使用正则表达式分割句子
    sentences = re.split(r'(?<=[.!?])\s+', text.strip())
    # 过滤空句子
    sentences = [s.strip() for s in sentences if s.strip()]
    return sentences

def process_subtitle_with_punctuation_check(srt_file_path):
    """带标点符号检查的字幕处理"""
    print(f"处理字幕文件: {srt_file_path}")
    print("=" * 60)
    
    if not os.path.exists(srt_file_path):
        print(f"❌ 文件不存在: {srt_file_path}")
        return False
    
    # 读取SRT文件
    with open(srt_file_path, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    print(f"文件大小: {len(srt_content)} 字符")
    
    # 步骤1: 检查整个文件是否包含标点符号
    if not check_file_has_punctuation(srt_content):
        print("\n🚫 字幕文件质量检查失败：没有标点符号")
        print("   建议：重新下载字幕文件")
        return False
    
    print("\n✅ 字幕文件质量检查通过，开始处理...")
    
    # 步骤2: 提取字幕内容并按句子分割
    subtitle_lines = []
    lines = srt_content.split('\n')
    
    for line in lines:
        line = line.strip()
        # 跳过序号、时间戳和空行
        if (line.isdigit() or 
            '-->' in line or 
            not line):
            continue
        subtitle_lines.append(line)
    
    # 合并所有字幕内容
    full_subtitle_text = ' '.join(subtitle_lines)
    print(f"\n合并的字幕文本: '{full_subtitle_text[:100]}{'...' if len(full_subtitle_text) > 100 else ''}'")
    
    # 步骤3: 按句子分割
    sentences = split_sentences_correctly(full_subtitle_text)
    print(f"\n分割出 {len(sentences)} 个句子:")
    for i, sentence in enumerate(sentences, 1):
        print(f"  {i}. '{sentence}'")
    
    # 步骤4: 翻译每个句子
    print(f"\n开始翻译 {len(sentences)} 个句子...")
    translated_sentences = []
    
    for i, sentence in enumerate(sentences, 1):
        try:
            if sentence in ['[Music]', '[Applause]', '[Laughter]']:
                translation_map = {
                    '[Music]': '[音乐]',
                    '[Applause]': '[掌声]',
                    '[Laughter]': '[笑声]'
                }
                translated = translation_map.get(sentence, sentence)
                print(f"  句子 {i}: '{sentence}' → '{translated}' (特殊标记)")
            else:
                translated = translator.translate(sentence)
                print(f"  句子 {i}: '{sentence}' → '{translated}'")
                time.sleep(0.3)
            
            translated_sentences.append(translated)
            
            # 检查41字符限制
            if len(translated) > 41:
                print(f"    ⚠️  超过41字符 ({len(translated)}字符)，需要拆分")
            else:
                print(f"    ✅ 符合41字符限制 ({len(translated)}字符)")
                
        except Exception as e:
            print(f"  句子 {i}: '{sentence}' → 翻译失败: {e}")
            return False
    
    print(f"\n✅ 所有句子翻译完成")
    print(f"翻译结果: {translated_sentences}")
    
    return True

def test_file_punctuation_check():
    """测试文件标点符号检查"""
    print("整个文件标点符号检查测试")
    print("=" * 60)
    
    # 测试用例1: 有标点符号的正常文件
    test_srt_with_punctuation = """1
00:00:00,000 --> 00:00:03,000
Hello world! How are you?

2
00:00:03,000 --> 00:00:06,000
I'm fine. Thank you."""
    
    # 测试用例2: 没有标点符号的文件
    test_srt_without_punctuation = """1
00:00:00,000 --> 00:00:03,000
Hello world How are you

2
00:00:03,000 --> 00:00:06,000
I am fine Thank you"""
    
    # 保存测试文件
    with open('test_with_punctuation.srt', 'w', encoding='utf-8') as f:
        f.write(test_srt_with_punctuation)
    
    with open('test_without_punctuation.srt', 'w', encoding='utf-8') as f:
        f.write(test_srt_without_punctuation)
    
    # 测试有标点符号的文件
    print("测试1: 有标点符号的文件")
    result1 = process_subtitle_with_punctuation_check('test_with_punctuation.srt')
    print(f"结果: {'成功' if result1 else '失败'}")
    
    print("\n" + "=" * 60)
    
    # 测试没有标点符号的文件
    print("测试2: 没有标点符号的文件")
    result2 = process_subtitle_with_punctuation_check('test_without_punctuation.srt')
    print(f"结果: {'成功' if result2 else '失败'}")
    
    # 清理测试文件
    try:
        os.remove('test_with_punctuation.srt')
        os.remove('test_without_punctuation.srt')
    except:
        pass
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"  有标点符号的文件: {'✅ 通过' if result1 else '❌ 失败'}")
    print(f"  无标点符号的文件: {'✅ 正确拒绝' if not result2 else '❌ 错误通过'}")

if __name__ == "__main__":
    test_file_punctuation_check()
