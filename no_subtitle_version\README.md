# YouTube视频下载系统 - 无字幕版本

这是YouTube视频下载系统的无字幕版本，专门用于只下载视频文件而不下载字幕的场景。

## 主要特点

### 与原版的区别
- ❌ **不下载字幕**：完全移除了字幕下载和处理功能
- ❌ **不进行字幕验证**：不会因为字幕质量问题而跳过视频下载
- ✅ **保留视频下载**：完整保留视频文件下载功能
- ✅ **保留封面下载**：继续下载视频缩略图
- ✅ **保留标题翻译**：继续翻译视频标题为中文
- ✅ **保留时间戳信息**：继续提取和保存视频元数据

### 下载流程
1. **检查直播状态** - 跳过直播和预告视频
2. **翻译标题** - 将英文标题翻译为中文
3. **下载封面** - 下载视频缩略图到thumbnails文件夹
4. **提取时间戳** - 获取视频时长、上传日期等信息
5. **下载视频** - 下载720p质量的MP4视频文件

### 文件结构
```
no_subtitle_version/
├── main.py                    # 程序入口
├── config.py                  # 配置文件（已移除字幕目录配置）
├── monitor.py                 # 监控逻辑
├── downloader.py              # 下载器（已移除字幕下载）
├── video_info.py              # 视频信息获取
├── translation.py             # 标题翻译
├── thumbnail_downloader.py    # 缩略图下载
├── utils.py                   # 工具函数
├── channels_videos_test.txt   # 频道配置
├── keywords.txt               # 过滤关键词
├── downloaded_videos.txt      # 下载历史
└── thumbnails/                # 缩略图保存目录
```

## 使用方法

1. **安装依赖**
   ```bash
   pip install feedparser httpx googletrans==4.0.0-rc1 deep-translator fuzzywuzzy requests
   ```

2. **安装yt-dlp**
   ```bash
   pip install yt-dlp
   ```

3. **配置频道列表**
   编辑 `channels_videos_test.txt` 文件，添加要监控的频道

4. **运行程序**
   ```bash
   python main.py
   ```

## 配置说明

### 主要配置项（config.py）
- `DOWNLOAD_PATHS`: 视频下载路径
- `CHECK_INTERVAL`: 检查间隔（秒）
- `USE_PROXY`: 是否使用代理
- `TRANSLATE_API_URL`: 翻译API地址

### 频道配置格式
```
频道名称 频道ID 检查时间(小时) 下载类型 额外信息
```

示例：
```
222 UCTdHK0UyGn028R18brPdogA 24 required 222
```

## 适用场景

- 只需要视频文件，不需要字幕
- 网络环境下字幕下载不稳定
- 存储空间有限，不想保存字幕文件
- 批量下载视频用于其他用途

## 注意事项

1. **无字幕验证**：由于不下载字幕，无法通过字幕质量来判断视频质量
2. **下载速度更快**：跳过字幕下载步骤，整体下载速度更快
3. **存储空间更小**：不保存VTT和SRT字幕文件，节省存储空间
4. **兼容性**：与原版使用相同的配置文件格式和目录结构

## 技术细节

### 移除的功能
- `subtitle.py` 模块（字幕处理）
- 字幕下载命令和验证逻辑
- VTT和SRT文件处理
- 中文字符数量检查

### 保留的功能
- 视频质量选择（720p）
- 代理支持
- 错误重试机制
- 下载历史记录
- 关键词过滤

## 版本信息

- 基于原版YouTube下载系统
- 移除字幕功能的简化版本
- 适用于纯视频下载需求
