
=== 🟡 Headers ===
{'Host': 'localhost:8080', 'Connection': 'keep-alive', 'Content-Length': '752', 'Api-Key': 'sk-mJtHjWKwIU10u3kiEfA3D22838A847079cBb66B88e2187A6', 'Authorization': 'Bearer sk-mJtHjWKwIU10u3kiEfA3D22838A847079cBb66B88e2187A6', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Content-Type': 'application/json', 'Accept': '*/*', 'Origin': 'chrome-extension://bpoadfkcbjbfhfodiogcnhhhpibjhbnh', 'Sec-Fetch-Site': 'none', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Storage-Access': 'active', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-CN,zh;q=0.9,zh-TW;q=0.8', 'Cookie': 'b-user-id=5b5c8e5d-d437-4583-b58c-947ff4e6c5aa'}

=== 🟢 JSON Payload ===
{'model': 'gpt-4o', 'temperature': 1, 'messages': [{'role': 'system', 'content': '\nThis is a general tarot reading recording. You need to first divide it into short and long sentences, add periods and commas, and ensure the sentence structure is coherent. The text should flow smoothly, like a novel. Any unclear parts of the meaning should be omitted. Only the translated text should be provided, without any extra guidance.\n\n'}, {'role': 'user', 'content': ';; 把下一行文本作为纯文本输入，并将其翻译为简体中文，仅输出翻译。如果某些 
内容无需翻译（如专有名词、代码等），则保持原文不变。不要解释，输入文本:\n Hello World.'}]}

=== 🔵 Response from Real API (decoded) ===
200
{"id":"chatcmpl-d2389951489648d1a9e19d902467c465","model":"gemini-1.5-pro-latest","object":"chat.completion","created":1745522564,"choices":[{"index":0,"message":{"role":"assistant","content":"你好，世界。\n"},"finish_reason":"stop"}],"usage":{"prompt_tokens":119,"completion_tokens":5,"total_tokens":124,"prompt_tokens_details":{"cached_tokens":0,"text_tokens":0,"audio_tokens":0,"image_tokens":0},"completion_tokens_details":{"text_tokens":0,"audio_tokens":0,"reasoning_tokens":0}}}
127.0.0.1 - - [25/Apr/2025 03:22:37] "POST /v1/chat/completions HTTP/1.1" 200 -

 *  还原的历史记录 

PS D:\ytb_python_download\ytb_download_modules> 