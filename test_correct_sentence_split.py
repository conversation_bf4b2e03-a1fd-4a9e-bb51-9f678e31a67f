#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的句子分割：按句号分别翻译每个句子
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def parse_srt_blocks(srt_content):
    """解析SRT文件为字幕块"""
    blocks = []
    srt_blocks = srt_content.strip().split('\n\n')
    
    for block in srt_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            index = int(lines[0])
            timestamp = lines[1]
            content_lines = lines[2:]
            content = ' '.join(content_lines).strip()
            if content:
                blocks.append({
                    'index': index,
                    'timestamp': timestamp,
                    'content': content
                })
    
    return blocks

def find_individual_sentences(blocks):
    """找到每个独立的句子，按句号分割"""
    sentences = []
    i = 0
    
    while i < len(blocks):
        current_block = blocks[i]
        content = current_block['content'].strip()
        
        if not content:
            i += 1
            continue
            
        # 特殊标记单独处理
        if content in ['[Music]', '[Applause]', '[Laughter]']:
            sentences.append({
                'content': content,
                'start_block_index': i,
                'blocks_used': [i]
            })
            i += 1
            continue
        
        # 检查当前块中是否包含完整句子
        # 先收集完整的文本内容
        full_text = content
        blocks_used = [i]
        j = i + 1
        
        # 如果当前块不以句号结尾，继续收集
        while j < len(blocks) and not re.search(r'[.!?]$', content):
            if j < len(blocks):
                next_block = blocks[j]
                next_content = next_block['content'].strip()
                if next_content:
                    full_text += ' ' + next_content
                    blocks_used.append(j)
                    content = next_content  # 更新content用于检查句号
                j += 1
            else:
                break
        
        # 现在在full_text中查找所有句子
        print(f"处理文本: '{full_text}'")
        
        # 按句号分割句子
        sentence_parts = re.split(r'([.!?])', full_text)
        current_sentence = ""
        sentence_start_block = i
        
        for part_idx in range(0, len(sentence_parts), 2):
            if part_idx < len(sentence_parts):
                sentence_text = sentence_parts[part_idx].strip()
                if part_idx + 1 < len(sentence_parts):
                    punctuation = sentence_parts[part_idx + 1]
                    sentence_text += punctuation
                
                if sentence_text:
                    sentences.append({
                        'content': sentence_text,
                        'start_block_index': sentence_start_block,
                        'blocks_used': blocks_used.copy()
                    })
                    print(f"  找到句子: '{sentence_text}'")
                    
                    # 下一个句子的起始位置需要估算
                    # 简化处理：如果有多个句子，后面的句子起始块向后推移
                    if part_idx + 2 < len(sentence_parts):
                        sentence_start_block = min(sentence_start_block + 1, len(blocks) - 1)
        
        # 如果没有找到句子（没有句号），整个作为一个句子
        if not sentences or sentences[-1]['start_block_index'] != i:
            sentences.append({
                'content': full_text,
                'start_block_index': i,
                'blocks_used': blocks_used
            })
            print(f"  无句号句子: '{full_text}'")
        
        i = j
    
    return sentences

def split_long_text(text, max_length=41):
    """将超长文本按41字符限制拆分"""
    if len(text) <= max_length:
        return [text]
    
    parts = []
    while len(text) > max_length:
        split_pos = max_length
        
        # 向前查找合适的断点
        for i in range(max_length - 1, max(0, max_length - 10), -1):
            if text[i] in '，。！？；：、 ':
                split_pos = i + 1
                break
        
        parts.append(text[:split_pos].strip())
        text = text[split_pos:].strip()
    
    if text:
        parts.append(text)
    
    return parts

def translate_individual_sentences(sentences, all_blocks):
    """翻译每个独立句子"""
    result_blocks = []
    used_block_indices = set()
    
    for i, sentence in enumerate(sentences, 1):
        content = sentence['content'].strip()
        start_block_index = sentence['start_block_index']
        
        print(f"\n句子 {i}: '{content}'")
        print(f"   起始块: {start_block_index + 1}")
        
        # 检查是否需要翻译
        has_punctuation = bool(re.search(r'[.!?,:;]', content))
        
        if not has_punctuation and content not in ['[Music]', '[Applause]', '[Laughter]']:
            print(f"   ⚠️  无标点符号，跳过翻译")
            translated_content = content
        else:
            # 翻译句子
            try:
                if content in ['[Music]', '[Applause]', '[Laughter]']:
                    translation_map = {
                        '[Music]': '[音乐]',
                        '[Applause]': '[掌声]',
                        '[Laughter]': '[笑声]'
                    }
                    translated_content = translation_map.get(content, content)
                    print(f"   特殊标记: '{content}' → '{translated_content}'")
                else:
                    translated_content = translator.translate(content)
                    print(f"   翻译结果: '{translated_content}'")
                    time.sleep(0.3)
                
            except Exception as e:
                print(f"   ❌ 翻译失败: {e}")
                translated_content = content
        
        # 检查41字符限制
        print(f"   翻译长度: {len(translated_content)} 字符")
        if len(translated_content) > 41:
            print(f"   ⚠️  超过41字符限制，进行拆分")
            split_parts = split_long_text(translated_content, max_length=41)
            print(f"   拆分为 {len(split_parts)} 部分: {split_parts}")
        else:
            split_parts = [translated_content]
            print(f"   ✅ 符合41字符限制")
        
        # 将翻译结果放在起始块
        if start_block_index < len(all_blocks):
            start_block = all_blocks[start_block_index]
            result_blocks.append({
                'index': start_block['index'],
                'timestamp': start_block['timestamp'],
                'content': split_parts[0]
            })
            used_block_indices.add(start_block_index)
            print(f"   放置在块 [{start_block['index']}]: '{split_parts[0]}'")
            
            # 如果有多个部分，需要额外的块
            for part_idx, part in enumerate(split_parts[1:], 1):
                # 创建额外的块
                result_blocks.append({
                    'index': f"{start_block['index']}_part{part_idx + 1}",
                    'timestamp': start_block['timestamp'],
                    'content': part
                })
                print(f"   额外块 [{start_block['index']}_part{part_idx + 1}]: '{part}'")
    
    # 添加未使用的块（保持原文）
    for i, block in enumerate(all_blocks):
        if i not in used_block_indices:
            result_blocks.append({
                'index': block['index'],
                'timestamp': block['timestamp'],
                'content': block['content']
            })
            print(f"   保留原文块 [{block['index']}]: '{block['content']}'")
    
    return result_blocks

def test_correct_sentence_split():
    """测试正确的句子分割"""
    print("正确的句子分割测试")
    print("=" * 50)
    
    # 先用简单的例子测试
    test_text = "We're no strangers to love. You know the rules and so do I."
    print(f"测试文本: '{test_text}'")
    
    # 按句号分割
    sentence_parts = re.split(r'([.!?])', test_text)
    print(f"分割结果: {sentence_parts}")
    
    sentences = []
    for i in range(0, len(sentence_parts), 2):
        if i < len(sentence_parts):
            sentence_text = sentence_parts[i].strip()
            if i + 1 < len(sentence_parts):
                punctuation = sentence_parts[i + 1]
                sentence_text += punctuation
            
            if sentence_text:
                sentences.append(sentence_text)
    
    print(f"提取的句子: {sentences}")
    
    # 翻译每个句子
    for i, sentence in enumerate(sentences, 1):
        try:
            translated = translator.translate(sentence)
            print(f"句子 {i}: '{sentence}' → '{translated}'")
            time.sleep(0.3)
        except Exception as e:
            print(f"句子 {i}: '{sentence}' → 翻译失败: {e}")

if __name__ == "__main__":
    test_correct_sentence_split()
