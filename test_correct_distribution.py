#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的翻译分配：组合句子翻译，但保持原有字幕块结构
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def parse_srt_blocks(srt_content):
    """解析SRT文件为字幕块"""
    blocks = []
    srt_blocks = srt_content.strip().split('\n\n')
    
    for block in srt_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            index = int(lines[0])
            timestamp = lines[1]
            content_lines = lines[2:]
            content = ' '.join(content_lines).strip()
            if content:
                blocks.append({
                    'index': index,
                    'timestamp': timestamp,
                    'content': content
                })
    
    return blocks

def group_by_complete_sentences(blocks):
    """按完整句子组合，但记录每个块的位置"""
    sentences = []
    i = 0
    
    while i < len(blocks):
        current_block = blocks[i]
        content = current_block['content'].strip()
        
        if not content:
            i += 1
            continue
            
        # 特殊标记单独处理
        if content in ['[Music]', '[Applause]', '[Laughter]']:
            sentences.append({
                'blocks': [current_block],
                'content': content,
                'start_index': i,
                'end_index': i
            })
            i += 1
            continue
        
        # 开始组合句子
        sentence_blocks = [current_block]
        sentence_content = content
        start_index = i
        
        # 检查当前块是否以句号结尾
        if re.search(r'[.!?]$', content):
            sentences.append({
                'blocks': sentence_blocks,
                'content': sentence_content,
                'start_index': start_index,
                'end_index': i
            })
            i += 1
            continue
        
        # 向后查找句子结尾
        j = i + 1
        while j < len(blocks):
            next_block = blocks[j]
            next_content = next_block['content'].strip()
            
            if not next_content:
                j += 1
                continue
            
            sentence_blocks.append(next_block)
            sentence_content += ' ' + next_content
            
            if re.search(r'[.!?]$', next_content):
                sentences.append({
                    'blocks': sentence_blocks,
                    'content': sentence_content,
                    'start_index': start_index,
                    'end_index': j
                })
                i = j + 1
                break
            
            j += 1
        else:
            # 没找到句子结尾
            sentences.append({
                'blocks': sentence_blocks,
                'content': sentence_content,
                'start_index': start_index,
                'end_index': j - 1
            })
            i = j
    
    return sentences

def translate_and_distribute(sentences):
    """翻译句子并正确分配到原有字幕块"""
    result_blocks = []
    
    for i, sentence in enumerate(sentences, 1):
        content = sentence['content'].strip()
        blocks = sentence['blocks']
        
        print(f"句子 {i}: '{content[:50]}{'...' if len(content) > 50 else ''}'")
        print(f"   包含 {len(blocks)} 个块")
        
        # 检查是否需要翻译
        has_punctuation = bool(re.search(r'[.!?,:;]', content))
        
        if not has_punctuation and content not in ['[Music]', '[Applause]', '[Laughter]']:
            print(f"   ⚠️  无标点符号，跳过翻译")
            # 保持原文，按原有块分配
            for block in blocks:
                result_blocks.append({
                    'index': block['index'],
                    'timestamp': block['timestamp'],
                    'content': block['content']
                })
            continue
        
        # 翻译完整句子
        try:
            if content in ['[Music]', '[Applause]', '[Laughter]']:
                translation_map = {
                    '[Music]': '[音乐]',
                    '[Applause]': '[掌声]',
                    '[Laughter]': '[笑声]'
                }
                translated_content = translation_map.get(content, content)
                print(f"   特殊标记: '{content}' → '{translated_content}'")
            else:
                translated_content = translator.translate(content)
                print(f"   翻译结果: '{translated_content}'")
                time.sleep(0.3)
            
            # 将翻译结果分配到原有的字幕块
            distributed_blocks = distribute_translation_to_blocks(translated_content, blocks)
            result_blocks.extend(distributed_blocks)
            
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
            # 翻译失败，保持原文
            for block in blocks:
                result_blocks.append({
                    'index': block['index'],
                    'timestamp': block['timestamp'],
                    'content': block['content']
                })
    
    return result_blocks

def distribute_translation_to_blocks(translated_content, original_blocks):
    """将翻译内容分配到原有的字幕块中"""
    if len(original_blocks) == 1:
        # 只有一个块，直接返回
        return [{
            'index': original_blocks[0]['index'],
            'timestamp': original_blocks[0]['timestamp'],
            'content': translated_content
        }]
    
    # 多个块，需要分配
    words = translated_content.split()
    distributed_blocks = []
    
    if len(words) <= len(original_blocks):
        # 词数少于或等于块数，尽量每个块分配一些词
        words_per_block = max(1, len(words) // len(original_blocks))
        word_index = 0
        
        for i, block in enumerate(original_blocks):
            if word_index < len(words):
                # 计算当前块应该分配的词数
                remaining_blocks = len(original_blocks) - i
                remaining_words = len(words) - word_index
                current_words_count = max(1, remaining_words // remaining_blocks)
                
                block_words = words[word_index:word_index + current_words_count]
                block_content = ' '.join(block_words)
                
                distributed_blocks.append({
                    'index': block['index'],
                    'timestamp': block['timestamp'],
                    'content': block_content
                })
                
                word_index += current_words_count
            else:
                # 没有更多词了，使用空内容或省略号
                distributed_blocks.append({
                    'index': block['index'],
                    'timestamp': block['timestamp'],
                    'content': '...'
                })
    else:
        # 词数多于块数，平均分配
        words_per_block = len(words) // len(original_blocks)
        remainder = len(words) % len(original_blocks)
        
        word_index = 0
        for i, block in enumerate(original_blocks):
            # 前面的块多分配一个词（如果有余数）
            current_words_count = words_per_block + (1 if i < remainder else 0)
            block_words = words[word_index:word_index + current_words_count]
            block_content = ' '.join(block_words)
            
            distributed_blocks.append({
                'index': block['index'],
                'timestamp': block['timestamp'],
                'content': block_content
            })
            
            word_index += current_words_count
    
    return distributed_blocks

def rebuild_srt_from_blocks(blocks):
    """从字幕块重建SRT"""
    srt_lines = []
    
    for block in blocks:
        srt_lines.append(str(block['index']))
        srt_lines.append(block['timestamp'])
        srt_lines.append(block['content'])
        srt_lines.append('')
    
    return '\n'.join(srt_lines)

def test_correct_distribution():
    """测试正确的翻译分配"""
    print("正确的翻译分配测试")
    print("=" * 50)
    
    english_srt = "real_test_improved.en.srt"
    chinese_srt = "correct_distribution.zh-Hans.srt"
    
    if not os.path.exists(english_srt):
        print(f"❌ 英文SRT文件不存在: {english_srt}")
        return False
    
    print(f"✅ 找到英文SRT文件: {english_srt}")
    
    # 读取英文SRT
    with open(english_srt, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    # 解析为字幕块
    blocks = parse_srt_blocks(srt_content)
    print(f"解析出 {len(blocks)} 个有效字幕块")
    
    # 按完整句子组合
    sentences = group_by_complete_sentences(blocks)
    print(f"组合成 {len(sentences)} 个完整句子")
    
    # 只处理前3个句子进行测试
    test_sentences = sentences[:3]
    print(f"\n测试前 {len(test_sentences)} 个句子:")
    for i, sentence in enumerate(test_sentences, 1):
        print(f"  {i}. '{sentence['content'][:80]}{'...' if len(sentence['content']) > 80 else ''}'")
        print(f"     块范围: {sentence['start_index']+1}-{sentence['end_index']+1} (共{len(sentence['blocks'])}个)")
    
    # 翻译并分配
    print(f"\n开始翻译和分配...")
    result_blocks = translate_and_distribute(test_sentences)
    
    # 重建SRT
    chinese_srt_content = rebuild_srt_from_blocks(result_blocks)
    
    # 保存
    with open(chinese_srt, 'w', encoding='utf-8') as f:
        f.write(chinese_srt_content)
    
    print(f"\n✅ 正确的翻译分配完成")
    print(f"   输出文件: {chinese_srt}")
    print(f"   生成了 {len(result_blocks)} 个字幕块")
    
    # 显示结果
    print("\n翻译分配结果:")
    print("-" * 60)
    print(chinese_srt_content)
    print("-" * 60)
    
    return True

if __name__ == "__main__":
    test_correct_distribution()
