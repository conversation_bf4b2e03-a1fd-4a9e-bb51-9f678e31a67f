#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的句子分割测试
"""

import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def split_sentences_correctly(text):
    """正确分割句子"""
    print(f"原文: '{text}'")
    
    # 使用正则表达式分割句子
    # 按句号、问号、感叹号分割，但保留标点符号
    sentences = re.split(r'(?<=[.!?])\s+', text.strip())
    
    # 过滤空句子
    sentences = [s.strip() for s in sentences if s.strip()]
    
    print(f"分割结果: {sentences}")
    return sentences

def test_sentence_splitting():
    """测试句子分割"""
    print("句子分割测试")
    print("=" * 40)
    
    test_cases = [
        "We're no strangers to love. You know the rules and so do I.",
        "Never going to give you up. I'm going to let you down. I'm going to run around and desert you.",
        "Hello world! How are you? I'm fine.",
        "This is a test sentence without punctuation",
        "[Music]"
    ]
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n测试 {i}:")
        sentences = split_sentences_correctly(test_text)
        
        # 翻译每个句子
        for j, sentence in enumerate(sentences, 1):
            try:
                if sentence in ['[Music]', '[Applause]', '[Laughter]']:
                    translation_map = {
                        '[Music]': '[音乐]',
                        '[Applause]': '[掌声]',
                        '[Laughter]': '[笑声]'
                    }
                    translated = translation_map.get(sentence, sentence)
                    print(f"  句子 {j}: '{sentence}' → '{translated}' (特殊标记)")
                else:
                    # 检查是否有标点符号
                    has_punctuation = bool(re.search(r'[.!?,:;]', sentence))
                    if not has_punctuation:
                        print(f"  句子 {j}: '{sentence}' → 跳过翻译 (无标点符号)")
                        continue
                    
                    translated = translator.translate(sentence)
                    print(f"  句子 {j}: '{sentence}' → '{translated}'")
                    
                    # 检查41字符限制
                    if len(translated) > 41:
                        print(f"    ⚠️  超过41字符 ({len(translated)}字符)，需要拆分")
                    else:
                        print(f"    ✅ 符合41字符限制 ({len(translated)}字符)")
                    
                    time.sleep(0.3)
                    
            except Exception as e:
                print(f"  句子 {j}: '{sentence}' → 翻译失败: {e}")

if __name__ == "__main__":
    test_sentence_splitting()
