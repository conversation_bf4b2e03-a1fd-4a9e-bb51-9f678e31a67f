#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试一次性翻译所有句子
"""

import os
import re
import time
import requests
import json

class AllAtOnceTranslator:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
    
    def translate_all_sentences(self, sentences):
        """一次性翻译所有句子"""
        print(f"准备一次性翻译 {len(sentences)} 个句子...")
        
        # 构建prompt
        sentences_text = ""
        for i, sentence in enumerate(sentences, 1):
            sentences_text += f"{i}. {sentence}\n"
        
        prompt = f"""请将以下{len(sentences)}个英文句子翻译成中文。

要求：
1. 翻译要自然流畅
2. 保持原文语气和情感
3. 严格按照序号返回
4. 格式：序号. 中文翻译

英文原文：
{sentences_text}

中文翻译："""

        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.2,
                "topK": 1,
                "topP": 1,
                "maxOutputTokens": 8192,  # 最大输出
            }
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            print("发送API请求...")
            start_time = time.time()
            
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers=headers,
                data=json.dumps(payload),
                timeout=180  # 3分钟超时
            )
            
            end_time = time.time()
            print(f"API响应时间: {end_time - start_time:.2f}秒")
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text']
                    return self.parse_result(content, len(sentences))
                else:
                    raise Exception("API返回格式错误")
            else:
                raise Exception(f"API错误: {response.status_code} - {response.text}")
                
        except Exception as e:
            raise Exception(f"翻译失败: {str(e)}")
    
    def parse_result(self, content, expected_count):
        """解析翻译结果"""
        print(f"解析翻译结果...")
        print(f"返回内容长度: {len(content)} 字符")
        print(f"前200字符: {content[:200]}...")
        
        # 按行分割
        lines = content.split('\n')
        translations = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 匹配格式：数字. 翻译内容
            match = re.match(r'^(\d+)\.\s*(.+)$', line)
            if match:
                index = int(match.group(1))
                translation = match.group(2).strip()
                translations.append((index, translation))
        
        print(f"成功解析 {len(translations)} 个翻译")
        
        # 按序号排序
        translations.sort(key=lambda x: x[0])
        result = [t[1] for t in translations]
        
        print(f"期望: {expected_count}, 实际: {len(result)}")
        
        return result

def test_all_at_once():
    """测试一次性翻译所有句子"""
    print("一次性翻译所有句子测试")
    print("=" * 50)
    
    # API密钥
    api_key = "AIzaSyBrEmZW9wqmyil7xPsbK2fgMb7LjII8NlQ"
    translator = AllAtOnceTranslator(api_key)
    
    # 读取句子
    srt_file = "complete_english.en.srt"
    
    if not os.path.exists(srt_file):
        print(f"❌ 文件不存在: {srt_file}")
        return False
    
    # 提取句子
    with open(srt_file, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    subtitle_lines = []
    lines = srt_content.split('\n')
    
    for line in lines:
        line = line.strip()
        if (line.isdigit() or '-->' in line or not line):
            continue
        subtitle_lines.append(line)
    
    full_text = ' '.join(subtitle_lines)
    sentences = re.split(r'(?<=[.!?])\s+', full_text.strip())
    sentences = [s.strip() for s in sentences if s.strip()]
    
    print(f"总共 {len(sentences)} 个句子")
    
    # 显示前几个句子
    print("\n前10个句子:")
    for i, sentence in enumerate(sentences[:10], 1):
        print(f"   {i:2d}. '{sentence[:60]}{'...' if len(sentence) > 60 else ''}'")
    
    try:
        print(f"\n开始一次性翻译所有 {len(sentences)} 个句子...")
        start_time = time.time()
        
        translations = translator.translate_all_sentences(sentences)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n🎉 一次性翻译成功!")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"平均每句: {total_time / len(sentences):.3f}秒")
        print(f"翻译速度: {len(sentences) / total_time:.2f} 句/秒")
        
        # 检查结果
        success_count = len(translations)
        long_count = sum(1 for t in translations if len(t) > 41)
        
        print(f"\n翻译统计:")
        print(f"   成功翻译: {success_count}/{len(sentences)}")
        print(f"   超过41字符: {long_count}")
        
        # 显示前10个结果
        print(f"\n前10个翻译结果:")
        for i, (original, translated) in enumerate(zip(sentences[:10], translations[:10]), 1):
            print(f"   {i:2d}. '{translated}' ({len(translated)}字符)")
        
        # 保存结果
        output_file = f"all_at_once_result.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"一次性翻译所有句子测试结果\n")
            f.write(f"总句子数: {len(sentences)}\n")
            f.write(f"成功翻译: {success_count}\n")
            f.write(f"总耗时: {total_time:.2f}秒\n")
            f.write(f"翻译速度: {len(sentences) / total_time:.2f} 句/秒\n\n")
            
            for i, (original, translated) in enumerate(zip(sentences, translations), 1):
                f.write(f"{i:2d}. {original}\n")
                f.write(f"    → {translated} ({len(translated)}字符)\n\n")
        
        print(f"结果已保存到: {output_file}")
        
        # 与之前的批量翻译对比
        print(f"\n性能对比:")
        print(f"   一次性翻译: {len(sentences) / total_time:.2f} 句/秒")
        print(f"   8句批量: 3.69 句/秒")
        print(f"   提升倍数: {(len(sentences) / total_time) / 3.69:.2f}x")
        
        return True
        
    except Exception as e:
        print(f"❌ 一次性翻译失败: {e}")
        return False

if __name__ == "__main__":
    test_all_at_once()
