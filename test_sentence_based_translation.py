#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基于句子的翻译 - 按句号组合字幕后翻译
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def parse_srt_blocks(srt_content):
    """解析SRT文件为字幕块"""
    blocks = []
    srt_blocks = srt_content.strip().split('\n\n')
    
    for block in srt_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            index = lines[0]
            timestamp = lines[1]
            content_lines = lines[2:]
            content = ' '.join(content_lines)  # 合并多行内容
            blocks.append({
                'index': index,
                'timestamp': timestamp,
                'content': content
            })
    
    return blocks

def group_by_sentences(blocks):
    """按句号组合字幕块为完整句子"""
    sentences = []
    current_sentence = {
        'blocks': [],
        'content': '',
        'start_timestamp': '',
        'end_timestamp': ''
    }
    
    for block in blocks:
        content = block['content'].strip()
        
        # 跳过空内容或特殊标记
        if not content or content in ['[Music]', '[Applause]', '[Laughter]']:
            # 如果当前句子有内容，先保存
            if current_sentence['content']:
                sentences.append(current_sentence.copy())
                current_sentence = {'blocks': [], 'content': '', 'start_timestamp': '', 'end_timestamp': ''}
            
            # 单独处理特殊标记
            sentences.append({
                'blocks': [block],
                'content': content,
                'start_timestamp': block['timestamp'],
                'end_timestamp': block['timestamp']
            })
            continue
        
        # 添加到当前句子
        if not current_sentence['content']:
            current_sentence['start_timestamp'] = block['timestamp']
        
        current_sentence['blocks'].append(block)
        current_sentence['content'] += (' ' + content if current_sentence['content'] else content)
        current_sentence['end_timestamp'] = block['timestamp']
        
        # 检查是否句子结束（包含句号、问号、感叹号）
        if re.search(r'[.!?]$', content):
            sentences.append(current_sentence.copy())
            current_sentence = {'blocks': [], 'content': '', 'start_timestamp': '', 'end_timestamp': ''}
    
    # 处理最后一个未完成的句子
    if current_sentence['content']:
        sentences.append(current_sentence)
    
    return sentences

def translate_sentences(sentences):
    """翻译句子"""
    translated_sentences = []
    
    for i, sentence in enumerate(sentences, 1):
        content = sentence['content'].strip()
        
        print(f"句子 {i}: '{content}'")
        
        # 检查是否包含标点符号
        has_punctuation = bool(re.search(r'[.!?,:;]', content))
        
        if not has_punctuation and content not in ['[Music]', '[Applause]', '[Laughter]']:
            print(f"   ⚠️  无标点符号，跳过翻译: '{content}'")
            translated_sentences.append({
                **sentence,
                'translated_content': content,  # 保持原文
                'translation_status': 'skipped_no_punctuation'
            })
            continue
        
        # 翻译
        try:
            if content in ['[Music]', '[Applause]', '[Laughter]']:
                # 特殊标记的翻译
                translation_map = {
                    '[Music]': '[音乐]',
                    '[Applause]': '[掌声]',
                    '[Laughter]': '[笑声]'
                }
                translated_content = translation_map.get(content, content)
                print(f"   特殊标记翻译: '{content}' -> '{translated_content}'")
            else:
                translated_content = translator.translate(content)
                print(f"   翻译结果: '{translated_content}'")
                time.sleep(0.3)  # 避免API限制
            
            translated_sentences.append({
                **sentence,
                'translated_content': translated_content,
                'translation_status': 'success'
            })
            
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
            translated_sentences.append({
                **sentence,
                'translated_content': content,  # 保持原文
                'translation_status': 'failed'
            })
    
    return translated_sentences

def rebuild_srt_from_sentences(translated_sentences):
    """从翻译后的句子重建SRT"""
    srt_lines = []
    
    for sentence in translated_sentences:
        blocks = sentence['blocks']
        translated_content = sentence['translated_content']
        
        if len(blocks) == 1:
            # 单个字幕块
            block = blocks[0]
            srt_lines.append(block['index'])
            srt_lines.append(block['timestamp'])
            srt_lines.append(translated_content)
            srt_lines.append('')
        else:
            # 多个字幕块组成的句子，需要拆分翻译内容
            words = translated_content.split()
            words_per_block = max(1, len(words) // len(blocks))
            
            for i, block in enumerate(blocks):
                start_word = i * words_per_block
                end_word = start_word + words_per_block if i < len(blocks) - 1 else len(words)
                block_content = ' '.join(words[start_word:end_word])
                
                srt_lines.append(block['index'])
                srt_lines.append(block['timestamp'])
                srt_lines.append(block_content)
                srt_lines.append('')
    
    return '\n'.join(srt_lines)

def translate_srt_by_sentences(english_srt_path, chinese_srt_path):
    """基于句子的SRT翻译"""
    print(f"开始基于句子的翻译: {english_srt_path} -> {chinese_srt_path}")
    
    try:
        # 读取英文SRT
        with open(english_srt_path, 'r', encoding='utf-8') as f:
            srt_content = f.read()
        
        print(f"原文件大小: {len(srt_content)} 字符")
        
        # 解析为字幕块
        blocks = parse_srt_blocks(srt_content)
        print(f"解析出 {len(blocks)} 个字幕块")
        
        # 按句号组合为句子
        sentences = group_by_sentences(blocks)
        print(f"组合成 {len(sentences)} 个句子")
        
        # 显示前几个句子
        print("\n前5个句子:")
        for i, sentence in enumerate(sentences[:5], 1):
            print(f"  {i}. '{sentence['content']}'")
            print(f"     时间: {sentence['start_timestamp']} (包含 {len(sentence['blocks'])} 个块)")
        
        # 翻译句子
        print(f"\n开始翻译 {len(sentences)} 个句子...")
        translated_sentences = translate_sentences(sentences)
        
        # 重建SRT
        chinese_srt_content = rebuild_srt_from_sentences(translated_sentences)
        
        # 保存中文SRT
        with open(chinese_srt_path, 'w', encoding='utf-8') as f:
            f.write(chinese_srt_content)
        
        print(f"\n✅ 基于句子的翻译完成")
        print(f"   输出文件: {chinese_srt_path}")
        print(f"   文件大小: {os.path.getsize(chinese_srt_path)} 字节")
        
        # 统计翻译结果
        success_count = sum(1 for s in translated_sentences if s['translation_status'] == 'success')
        skipped_count = sum(1 for s in translated_sentences if s['translation_status'] == 'skipped_no_punctuation')
        failed_count = sum(1 for s in translated_sentences if s['translation_status'] == 'failed')
        
        print(f"   翻译统计: 成功 {success_count}, 跳过 {skipped_count}, 失败 {failed_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基于句子的翻译出错: {e}")
        return False

def main():
    """主测试函数"""
    print("基于句子的SRT翻译测试")
    print("=" * 50)
    
    # 使用现有的英文SRT文件
    english_srt = "real_test_improved.en.srt"
    chinese_srt = "real_test_sentence_based.zh-Hans.srt"
    
    if not os.path.exists(english_srt):
        print(f"❌ 英文SRT文件不存在: {english_srt}")
        return 1
    
    print(f"✅ 找到英文SRT文件: {english_srt}")
    
    # 执行基于句子的翻译
    if translate_srt_by_sentences(english_srt, chinese_srt):
        print("\n🎉 基于句子的翻译测试成功！")
        
        # 对比翻译效果
        print("\n=== 翻译效果对比 ===")
        if os.path.exists(chinese_srt):
            with open(chinese_srt, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 显示前几个字幕块
            blocks = content.strip().split('\n\n')[:5]
            for i, block in enumerate(blocks, 1):
                lines = block.strip().split('\n')
                print(f"\n字幕块 {i}:")
                for line in lines:
                    print(f"   {line}")
        
        return 0
    else:
        print("\n❌ 基于句子的翻译失败")
        return 1

if __name__ == "__main__":
    exit(main())
