#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试Gemini API密钥和翻译效果
"""

import time
import requests
import json

class GeminiTranslator:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
    
    def translate(self, text):
        """使用Gemini翻译文本"""
        prompt = f"""请将以下英文翻译成中文，要求：
1. 翻译要自然流畅
2. 保持原文的语气和情感
3. 只返回翻译结果，不要其他解释

英文原文：{text}

中文翻译："""

        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.3,
                "topK": 1,
                "topP": 1,
                "maxOutputTokens": 1024,
            }
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers=headers,
                data=json.dumps(payload),
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text']
                    content = content.strip()
                    # 清理可能的前缀
                    if content.startswith('中文翻译：'):
                        content = content[5:].strip()
                    elif content.startswith('翻译：'):
                        content = content[3:].strip()
                    return content
                else:
                    raise Exception("Gemini API返回格式错误")
            else:
                raise Exception(f"Gemini API错误: {response.status_code} - {response.text}")
                
        except Exception as e:
            raise Exception(f"Gemini翻译失败: {str(e)}")

def test_gemini_direct():
    """直接测试Gemini翻译"""
    print("Gemini API直接测试")
    print("=" * 40)
    
    # 使用提供的API密钥
    api_key = "AIzaSyBrEmZW9wqmyil7xPsbK2fgMb7LjII8NlQ"
    print(f"API密钥: {api_key[:10]}...")
    
    # 初始化翻译器
    translator = GeminiTranslator(api_key)
    
    # 测试句子
    test_sentences = [
        "We're no strangers to love.",
        "You know the rules and so do I.",
        "Never gonna give you up."
    ]
    
    print(f"\n开始测试翻译 {len(test_sentences)} 个句子...")
    
    success_count = 0
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n句子 {i}: '{sentence}'")
        
        try:
            start_time = time.time()
            translated = translator.translate(sentence)
            end_time = time.time()
            
            print(f"   翻译结果: '{translated}'")
            print(f"   耗时: {end_time - start_time:.2f}秒")
            print(f"   长度: {len(translated)} 字符")
            
            if len(translated) > 41:
                print(f"   ⚠️  超过41字符限制")
            else:
                print(f"   ✅ 符合41字符限制")
            
            success_count += 1
            time.sleep(1)  # 避免API限制
            
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
    
    print(f"\n" + "=" * 40)
    print(f"测试结果:")
    print(f"   成功: {success_count}/{len(test_sentences)}")
    print(f"   失败: {len(test_sentences) - success_count}")
    
    if success_count > 0:
        print(f"✅ Gemini API可用，翻译质量良好")
        return True
    else:
        print(f"❌ Gemini API不可用或有问题")
        return False

if __name__ == "__main__":
    test_gemini_direct()
