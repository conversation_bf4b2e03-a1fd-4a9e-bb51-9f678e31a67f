#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的按句子组合翻译
向前向后扫描，组合完整句子
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def parse_srt_blocks(srt_content):
    """解析SRT文件为字幕块"""
    blocks = []
    srt_blocks = srt_content.strip().split('\n\n')
    
    for block in srt_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            index = int(lines[0])
            timestamp = lines[1]
            content_lines = lines[2:]
            content = ' '.join(content_lines).strip()  # 合并多行内容
            if content:  # 只保留有内容的块
                blocks.append({
                    'index': index,
                    'timestamp': timestamp,
                    'content': content
                })
    
    return blocks

def group_by_complete_sentences(blocks):
    """正确的按完整句子组合"""
    sentences = []
    i = 0
    
    while i < len(blocks):
        current_block = blocks[i]
        content = current_block['content'].strip()
        
        # 跳过空内容
        if not content:
            i += 1
            continue
            
        # 特殊标记单独处理
        if content in ['[Music]', '[Applause]', '[Laughter]']:
            sentences.append({
                'blocks': [current_block],
                'content': content,
                'start_timestamp': current_block['timestamp'],
                'end_timestamp': current_block['timestamp']
            })
            i += 1
            continue
        
        # 开始组合句子
        sentence_blocks = [current_block]
        sentence_content = content
        start_timestamp = current_block['timestamp']
        end_timestamp = current_block['timestamp']
        
        # 检查当前块是否以句号结尾
        if re.search(r'[.!?]$', content):
            # 当前块就是完整句子
            sentences.append({
                'blocks': sentence_blocks,
                'content': sentence_content,
                'start_timestamp': start_timestamp,
                'end_timestamp': end_timestamp
            })
            i += 1
            continue
        
        # 当前块不是完整句子，需要向后查找
        j = i + 1
        while j < len(blocks):
            next_block = blocks[j]
            next_content = next_block['content'].strip()
            
            if not next_content:
                j += 1
                continue
            
            # 添加到当前句子
            sentence_blocks.append(next_block)
            sentence_content += ' ' + next_content
            end_timestamp = next_block['timestamp']
            
            # 检查是否句子结束
            if re.search(r'[.!?]$', next_content):
                # 找到句子结尾
                sentences.append({
                    'blocks': sentence_blocks,
                    'content': sentence_content,
                    'start_timestamp': start_timestamp,
                    'end_timestamp': end_timestamp
                })
                i = j + 1  # 跳到下一个未处理的块
                break
            
            j += 1
        else:
            # 没有找到句子结尾，将当前收集的作为一个句子
            sentences.append({
                'blocks': sentence_blocks,
                'content': sentence_content,
                'start_timestamp': start_timestamp,
                'end_timestamp': end_timestamp
            })
            i = j
    
    return sentences

def translate_complete_sentences(sentences):
    """翻译完整句子"""
    translated_sentences = []
    
    for i, sentence in enumerate(sentences, 1):
        content = sentence['content'].strip()
        
        print(f"句子 {i}: '{content}'")
        
        # 检查是否包含标点符号
        has_punctuation = bool(re.search(r'[.!?,:;]', content))
        
        if not has_punctuation and content not in ['[Music]', '[Applause]', '[Laughter]']:
            print(f"   ⚠️  无标点符号，跳过翻译")
            translated_sentences.append({
                **sentence,
                'translated_content': content,  # 保持原文
                'translation_status': 'skipped_no_punctuation'
            })
            continue
        
        # 翻译
        try:
            if content in ['[Music]', '[Applause]', '[Laughter]']:
                # 特殊标记的翻译
                translation_map = {
                    '[Music]': '[音乐]',
                    '[Applause]': '[掌声]',
                    '[Laughter]': '[笑声]'
                }
                translated_content = translation_map.get(content, content)
                print(f"   特殊标记: '{content}' → '{translated_content}'")
            else:
                translated_content = translator.translate(content)
                print(f"   翻译结果: '{translated_content}'")
                time.sleep(0.3)  # 避免API限制
            
            translated_sentences.append({
                **sentence,
                'translated_content': translated_content,
                'translation_status': 'success'
            })
            
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
            translated_sentences.append({
                **sentence,
                'translated_content': content,  # 保持原文
                'translation_status': 'failed'
            })
    
    return translated_sentences

def rebuild_srt_from_translated_sentences(translated_sentences):
    """从翻译后的句子重建SRT"""
    srt_lines = []
    index_counter = 1
    
    for sentence in translated_sentences:
        blocks = sentence['blocks']
        translated_content = sentence['translated_content']
        
        if not translated_content.strip():
            continue  # 跳过空内容
        
        if len(blocks) == 1:
            # 单个字幕块
            block = blocks[0]
            srt_lines.append(str(index_counter))
            srt_lines.append(block['timestamp'])
            srt_lines.append(translated_content)
            srt_lines.append('')
            index_counter += 1
        else:
            # 多个字幕块组成的句子
            # 策略：将翻译内容平均分配到各个时间段
            words = translated_content.split()
            if len(words) <= len(blocks):
                # 词数少于或等于块数，每个块分配一个词或几个词
                words_per_block = 1
                for i, block in enumerate(blocks):
                    if i < len(words):
                        block_content = ' '.join(words[i:i+words_per_block])
                        if block_content.strip():
                            srt_lines.append(str(index_counter))
                            srt_lines.append(block['timestamp'])
                            srt_lines.append(block_content)
                            srt_lines.append('')
                            index_counter += 1
            else:
                # 词数多于块数，平均分配
                words_per_block = len(words) // len(blocks)
                remainder = len(words) % len(blocks)
                
                word_index = 0
                for i, block in enumerate(blocks):
                    # 计算当前块应该分配的词数
                    current_words_count = words_per_block + (1 if i < remainder else 0)
                    block_words = words[word_index:word_index + current_words_count]
                    block_content = ' '.join(block_words)
                    
                    if block_content.strip():
                        srt_lines.append(str(index_counter))
                        srt_lines.append(block['timestamp'])
                        srt_lines.append(block_content)
                        srt_lines.append('')
                        index_counter += 1
                    
                    word_index += current_words_count
    
    return '\n'.join(srt_lines)

def test_correct_sentence_grouping():
    """测试正确的句子组合"""
    print("正确的按句子组合翻译测试")
    print("=" * 50)
    
    # 使用现有的英文SRT文件
    english_srt = "real_test_improved.en.srt"
    chinese_srt = "correct_sentence_grouping.zh-Hans.srt"
    
    if not os.path.exists(english_srt):
        print(f"❌ 英文SRT文件不存在: {english_srt}")
        return False
    
    print(f"✅ 找到英文SRT文件: {english_srt}")
    
    # 读取英文SRT
    with open(english_srt, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    print(f"原文件大小: {len(srt_content)} 字符")
    
    # 解析为字幕块
    blocks = parse_srt_blocks(srt_content)
    print(f"解析出 {len(blocks)} 个有效字幕块")
    
    # 显示前几个块
    print("\n前10个字幕块:")
    for i, block in enumerate(blocks[:10], 1):
        print(f"  {i}. [{block['index']}] {block['timestamp']} '{block['content']}'")
    
    # 按完整句子组合
    sentences = group_by_complete_sentences(blocks)
    print(f"\n组合成 {len(sentences)} 个完整句子")
    
    # 显示前几个句子
    print("\n前5个完整句子:")
    for i, sentence in enumerate(sentences[:5], 1):
        print(f"  {i}. '{sentence['content']}'")
        print(f"     包含 {len(sentence['blocks'])} 个块: {[b['index'] for b in sentence['blocks']]}")
    
    # 翻译句子（只翻译前5个进行测试）
    print(f"\n开始翻译前5个句子...")
    test_sentences = sentences[:5]
    translated_sentences = translate_complete_sentences(test_sentences)
    
    # 重建SRT
    chinese_srt_content = rebuild_srt_from_translated_sentences(translated_sentences)
    
    # 保存中文SRT
    with open(chinese_srt, 'w', encoding='utf-8') as f:
        f.write(chinese_srt_content)
    
    print(f"\n✅ 正确的句子组合翻译完成")
    print(f"   输出文件: {chinese_srt}")
    print(f"   文件大小: {os.path.getsize(chinese_srt)} 字节")
    
    # 显示结果
    print("\n翻译结果:")
    print("-" * 40)
    print(chinese_srt_content)
    print("-" * 40)
    
    # 检查中文字符
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', chinese_srt_content)
    print(f"中文字符数: {len(chinese_chars)}")
    
    return True

if __name__ == "__main__":
    test_correct_sentence_grouping()
