#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Gemini翻译API
"""

import os
import re
import time
import requests
import json

class GeminiTranslator:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"
    
    def translate(self, text, target_language="中文"):
        """使用Gemini翻译文本"""
        prompt = f"""请将以下英文翻译成{target_language}，要求：
1. 翻译要自然流畅
2. 保持原文的语气和情感
3. 如果是歌词，保持韵律感
4. 只返回翻译结果，不要其他解释

英文原文：{text}

翻译："""

        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.3,
                "topK": 1,
                "topP": 1,
                "maxOutputTokens": 2048,
            }
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers=headers,
                data=json.dumps(payload),
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text']
                    # 清理返回的内容，只保留翻译结果
                    content = content.strip()
                    # 移除可能的前缀
                    if content.startswith('翻译：'):
                        content = content[3:].strip()
                    return content
                else:
                    raise Exception("Gemini API返回格式错误")
            else:
                raise Exception(f"Gemini API错误: {response.status_code} - {response.text}")
                
        except Exception as e:
            raise Exception(f"Gemini翻译失败: {str(e)}")

def test_gemini_translation():
    """测试Gemini翻译"""
    print("Gemini翻译测试")
    print("=" * 40)
    
    # 检查API密钥
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ 请设置GEMINI_API_KEY环境变量")
        print("   获取API密钥：https://makersuite.google.com/app/apikey")
        return False
    
    print(f"✅ 找到Gemini API密钥: {api_key[:10]}...")
    
    # 初始化翻译器
    translator = GeminiTranslator(api_key)
    
    # 测试句子
    test_sentences = [
        "We're no strangers to love.",
        "You know the rules and so do I.",
        "Never gonna give you up.",
        "Never gonna let you down.",
        "Never gonna run around and desert you."
    ]
    
    print(f"\n开始测试翻译 {len(test_sentences)} 个句子...")
    
    for i, sentence in enumerate(test_sentences, 1):
        try:
            print(f"\n句子 {i}: '{sentence}'")
            
            start_time = time.time()
            translated = translator.translate(sentence)
            end_time = time.time()
            
            print(f"   Gemini翻译: '{translated}'")
            print(f"   耗时: {end_time - start_time:.2f}秒")
            print(f"   长度: {len(translated)} 字符")
            
            if len(translated) > 41:
                print(f"   ⚠️  超过41字符限制")
            else:
                print(f"   ✅ 符合41字符限制")
            
            time.sleep(1)  # 避免API限制
            
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
    
    print(f"\n✅ Gemini翻译测试完成")
    return True

def compare_translators():
    """比较不同翻译器的效果"""
    print("\n" + "=" * 50)
    print("翻译器对比测试")
    print("=" * 50)
    
    # 检查API密钥
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ 请设置GEMINI_API_KEY环境变量")
        return False
    
    # 初始化翻译器
    gemini_translator = GeminiTranslator(api_key)
    
    try:
        from deep_translator import GoogleTranslator
        google_translator = GoogleTranslator(source='en', target='zh-CN')
    except ImportError:
        print("❌ 请安装deep-translator: pip install deep-translator")
        return False
    
    # 测试句子
    test_sentence = "We're no strangers to love. You know the rules and so do I."
    
    print(f"测试句子: '{test_sentence}'")
    print("-" * 50)
    
    # Google Translate
    try:
        google_result = google_translator.translate(test_sentence)
        print(f"Google Translate: '{google_result}' ({len(google_result)} 字符)")
    except Exception as e:
        print(f"Google Translate失败: {e}")
        google_result = None
    
    # Gemini
    try:
        gemini_result = gemini_translator.translate(test_sentence)
        print(f"Gemini翻译:      '{gemini_result}' ({len(gemini_result)} 字符)")
    except Exception as e:
        print(f"Gemini翻译失败: {e}")
        gemini_result = None
    
    print("-" * 50)
    
    if google_result and gemini_result:
        print("对比分析:")
        print(f"  Google: 更简洁，{len(google_result)} 字符")
        print(f"  Gemini: 更详细，{len(gemini_result)} 字符")
        
        if len(gemini_result) <= 41:
            print("  ✅ Gemini结果符合41字符限制")
        else:
            print("  ⚠️  Gemini结果超过41字符限制")
    
    return True

if __name__ == "__main__":
    # 首先测试基本功能
    if test_gemini_translation():
        # 然后对比不同翻译器
        compare_translators()
    
    print("\n" + "=" * 50)
    print("使用说明:")
    print("1. 获取Gemini API密钥: https://makersuite.google.com/app/apikey")
    print("2. 设置环境变量: set GEMINI_API_KEY=your_api_key")
    print("3. 运行测试: python test_gemini_translation.py")
