import os
import time
from datetime import timedelta
from config import logger, CHANNELS_VIDEOS_FILE, DOWNLOAD_PATHS, CHECK_INTERVAL
from utils import load_downloaded_history, load_keywords, get_keywords
from video_info import get_latest_videos_with_retry
from downloader import download_video
import subprocess

def get_video_duration(file_path):
    """获取视频时长（秒）"""
    try:
        # 使用ffprobe获取视频时长
        cmd = f'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{file_path}"'
        output = subprocess.check_output(cmd, shell=True).decode('utf-8').strip()
        return float(output) if output else None
    except Exception as e:
        logger.error(f"获取视频时长出错: {e}")
        return None

def process_channel(channel_name, channel_id, time_range, download_location, extra_info):
    """
    处理单个频道的视频下载逻辑 - 无字幕版本。
    """
    logger.warning(f"检查频道 {channel_name} 的新视频... ({extra_info})")
    try:
        videos = get_latest_videos_with_retry(channel_id, time_range)
    except Exception as e:
        logger.error(f"获取频道视频列表失败: {e}")
        return

    # 获取当前关键词列表（不输出日志）
    filter_keywords = get_keywords()
    
    for video_id, video_title, upload_date in videos:
        # 创建一个更简洁的过滤条件检查
        should_filter = False
        if channel_name != "222":
            for keyword in filter_keywords:
                if keyword.lower() in video_title.lower():
                    should_filter = True
                    break
            
            if should_filter:
                # 简化日志输出，仅在跳过时简单记录
                logger.warning(f"跳过视频: {video_title}")
                continue

        # 下载视频（无字幕版本）
        try:
            download_video(video_id, video_title, channel_name, download_location, upload_date)
        except Exception as e:
            logger.error(f"下载视频时出错: {e}")

def monitor_video_channels():
    """
    监控视频频道并下载新视频 - 无字幕版本
    """
    # 加载已下载的视频历史记录
    load_downloaded_history()
    
    # 加载关键词
    load_keywords()
    
    # 读取频道配置文件
    if not os.path.exists(CHANNELS_VIDEOS_FILE):
        logger.error(f"频道配置文件不存在: {CHANNELS_VIDEOS_FILE}")
        return
    
    with open(CHANNELS_VIDEOS_FILE, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('#'):
            continue
        
        try:
            parts = line.split()
            if len(parts) >= 4:
                channel_name = parts[0]
                channel_id = parts[1]
                time_range_hours = int(parts[2])
                download_type = parts[3]
                extra_info = ' '.join(parts[4:]) if len(parts) > 4 else ""
                
                # 转换时间范围
                time_range = timedelta(hours=time_range_hours)
                
                # 确定下载位置
                if download_type == "required":
                    download_location = DOWNLOAD_PATHS["required"]
                elif download_type == "alternative":
                    download_location = DOWNLOAD_PATHS["alternative"]
                else:
                    logger.error(f"未知的下载类型: {download_type}")
                    continue
                
                # 处理频道
                process_channel(channel_name, channel_id, time_range, download_location, extra_info)
            else:
                logger.error(f"频道配置格式错误: {line}")
        except Exception as e:
            logger.error(f"处理频道配置时出错: {line}, 错误: {e}")

def start_video_monitoring():
    """
    启动视频监控 - 无字幕版本
    """
    while True:
        try:
            logger.warning("开始检查视频...")
            monitor_video_channels()
        except Exception as e:
            logger.error(f"监控过程中发生错误：{e}，等待 {CHECK_INTERVAL} 秒后重试...")
        finally:
            minutes = CHECK_INTERVAL // 60
            seconds = CHECK_INTERVAL % 60
            logger.warning(f"检查完成，等待 {minutes} 分钟 {seconds} 秒后再次检查。")
            time.sleep(CHECK_INTERVAL)
