import time
import httpx
import feedparser
from datetime import datetime
import subprocess
from config import logger, USE_PROXY, PROXY_URL

def get_latest_videos(channel_id, time_range):
    """获取频道最新的视频信息"""
    RSS_FEED_URL = f"https://www.youtube.com/feeds/videos.xml?channel_id={channel_id}"
    feed = feedparser.parse(RSS_FEED_URL)
    videos = []
    current_time = datetime.utcnow()

    for entry in feed.entries:
        video_id = entry.yt_videoid
        video_title = entry.title
        upload_date = datetime.strptime(entry.published, '%Y-%m-%dT%H:%M:%S%z').replace(tzinfo=None)
        if current_time - upload_date <= time_range:
            videos.append((video_id, video_title, upload_date))

    return videos

def get_latest_videos_with_retry(channel_id, time_range, retries=5, delay=600):
    """获取频道最新的视频信息，带重试机制"""
    for attempt in range(retries):
        try:
            return get_latest_videos(channel_id, time_range)  # 调用原始方法
        except httpx.RequestError as e:
            logger.error(f"尝试连接失败，第 {attempt + 1} 次，等待 {delay} 秒后重试... 错误: {e}")
            if attempt < retries - 1:
                time.sleep(delay)  # 等待指定时间再重试
            else:
                logger.critical("已达最大重试次数，停止尝试。")
                raise
        except Exception as e:
            logger.error(f"获取视频列表出错: {e}")
            if attempt < retries - 1:
                time.sleep(delay)
            else:
                logger.critical("已达最大重试次数，停止尝试。")
                raise

def is_video_live(video_url):
    """检查视频是否正在直播或是直播预告"""
    try:
        # 构造检测直播状态的命令
        cmd = [
            "yt-dlp",
            "--cookies-from-browser", "firefox",
        ]
        if USE_PROXY:
            cmd += ["--proxy", PROXY_URL]
        cmd += [
            "--simulate",
            "--match-filter", "is_live",         # 仅匹配正在直播的视频
            video_url
        ]
        logger.warning(f"执行命令: {' '.join(cmd)}")  # 打印命令用于调试
        
        # 运行命令并捕获输出，使用try-except捕获异常
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            output = result.stdout
            logger.warning(f"命令输出: {output}")  # 打印命令输出内容

            # 检查命令输出是否包含 "does not pass filter" 以判断是否直播
            if "does not pass filter (is_live)" in output:
                logger.warning("视频不是直播")
                return False  # 视频不是直播
            else:
                logger.warning("检测到视频正在直播")
                return True  # 视频正在直播
                
        except subprocess.CalledProcessError as e:
            # 重要：检查错误输出中是否包含直播预告信息
            error_output = e.stderr
            logger.warning(f"命令执行失败，错误输出: {error_output}")
            
            # 检查是否包含直播预告信息
            if "Premieres in" in error_output or "premiere" in error_output.lower():
                logger.warning(f"检测到直播预告: {error_output}")
                return True  # 直播预告也视为直播，返回True以跳过下载
            
            logger.error(f"检测直播状态时命令执行失败: {e}")
            return False
            
    except Exception as e:
        logger.error(f"检测直播状态发生未知错误: {e}")
        return False 

def get_video_info(video_id):
    """获取单个视频的信息（标题、频道名等）"""
    try:
        video_url = f"https://www.youtube.com/watch?v={video_id}"
        
        # 构造获取视频信息的命令
        cmd = [
            "yt-dlp",
            "--cookies-from-browser", "firefox",
        ]
        if USE_PROXY:
            cmd += ["--proxy", PROXY_URL]
        cmd += [
            "--skip-download",
            "--print", "%(title)s,%(channel)s",
            video_url
        ]
        
        logger.warning(f"执行命令获取视频信息: {' '.join(cmd)}")
        
        # 运行命令并捕获输出
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            # 解析输出: 标题,频道名
            info_parts = result.stdout.strip().split(',', 1)  # 限制只分割第一个逗号
            if len(info_parts) >= 2:
                title = info_parts[0].strip()
                channel = info_parts[1].strip()
                
                logger.warning(f"获取到视频信息 - 标题: {title}, 频道: {channel}")
                
                return {
                    "title": title,
                    "channel": channel,
                    "video_id": video_id
                }
        
        logger.error(f"无法获取视频信息，yt-dlp输出: {result.stdout}")
        return None
    except Exception as e:
        logger.error(f"获取视频信息时出错: {e}")
        return None 