#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试：基于原逻辑的英文字幕下载+翻译
完全保持原来的逻辑，只是语言从zh-Hans改成en，最后加上谷歌翻译
"""

import os
import re
import time
import subprocess
from deep_translator import GoogleTranslator

# 配置
VTT_HAN_FOLDER = "test_subtitles"
SRT_HAN_FOLDER = "test_subtitles"
USE_PROXY = False
PROXY_URL = "http://127.0.0.1:7987"

# 创建测试目录
os.makedirs(VTT_HAN_FOLDER, exist_ok=True)
os.makedirs(SRT_HAN_FOLDER, exist_ok=True)

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def check_subtitle_size(file_path, min_size_kb=1):
    """检查字幕文件是否存在且大小是否符合要求 - 原逻辑保持不变"""
    try:
        if not os.path.exists(file_path):
            print(f"字幕文件不存在: {file_path}")
            return False
            
        file_size_kb = os.path.getsize(file_path) / 1024  # 转换为KB
        if file_size_kb < min_size_kb:
            print(f"字幕文件大小不足 {min_size_kb}KB: {file_path} (当前大小: {file_size_kb:.2f}KB)")
            return False
            
        print(f"字幕文件检查通过: {file_path} (大小: {file_size_kb:.2f}KB)")
        return True
    except Exception as e:
        print(f"检查字幕文件时出错: {e}")
        return False

def should_skip_line(line):
    """检查是否需要跳过当前行（无用信息） - 原逻辑保持不变"""
    if (
        not line  # 空行
        or line.startswith("WEBVTT")  # VTT 头部
        or line.startswith("Kind:")  # 字幕类型
        or line.startswith("Language:")  # 语言信息
    ):
        return True
    return False

def split_long_lines_to_time_blocks(text, max_length=50):
    """将长字幕拆分为多个时间块 - 原逻辑保持不变"""
    split_texts = []
    while len(text) > max_length:
        split_texts.append(text[:max_length])  # 分割字幕文本
        text = text[max_length:]  # 更新剩余文本
    split_texts.append(text)  # 添加剩余部分
    return split_texts

def convert_vtt_to_srt(vtt_file_path, srt_file_path, max_length=50):
    """将 .vtt 转成标准 .srt - 原逻辑完全保持不变"""
    try:
        if not os.path.exists(vtt_file_path):
            print(f"VTT 不存在: {vtt_file_path}")
            return

        with open(vtt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        cleaned_lines = []
        block = []
        index_counter = 1
        last_line_text = None

        for line in lines:
            line = line.strip()

            # 跳过头部 "WEBVTT"等
            if should_skip_line(line):
                continue

            # 如果这是时间戳行
            if '-->' in line:
                # 先把上一个block写入
                if block:
                    cleaned_lines.append(str(index_counter))
                    cleaned_lines.append(block[0])  # 时间戳
                    for c in block[1:]:
                        cleaned_lines.append(c)
                    cleaned_lines.append("")  # 添加空行
                    index_counter += 1
                    block = []  # 清空block

                # 修正vtt->srt时间戳
                line = line.replace('.', ',')  # 00:00:01.000 -> 00:00:01,000
                # 移除多余的 align: start position:0% 之类
                line = re.sub(r'\s*align:\w+|\s*position:\d+%', '', line).strip()
                block.append(line)
            else:
                # 非时间戳行(字幕内容)
                text = re.sub(r'<[^>]+>', '', line)  # 去掉HTML标签
                if text and text != last_line_text:
                    # 处理长行
                    splitted = split_long_lines_to_time_blocks(text, max_length=max_length)
                    block.extend(splitted)
                    last_line_text = text

        # 处理最后一个block
        if block:
            cleaned_lines.append(str(index_counter))
            cleaned_lines.append(block[0])  # 时间戳
            for c in block[1:]:
                cleaned_lines.append(c)
            cleaned_lines.append("")  # 添加空行

        # 写到 srt
        with open(srt_file_path, 'w', encoding='utf-8') as sf:
            for cl in cleaned_lines:
                sf.write(cl + '\n')

        print(f"已将 {vtt_file_path} 转成 {srt_file_path}")
    except Exception as e:
        print(f"convert_vtt_to_srt error: {e}")

def convert_srt_to_txt(srt_file_path, txt_file_path):
    """将 .srt 文件转换为纯文本 - 原逻辑保持不变"""
    try:
        if not os.path.exists(srt_file_path):
            print(f"转换失败，未找到 .srt 文件: {srt_file_path}")
            return

        with open(srt_file_path, 'r', encoding='utf-8') as srt_file:
            lines = srt_file.readlines()

        cleaned_lines = []
        for line in lines:
            # 跳过字幕序号和时间戳，只保留字幕文本
            if line.strip().isdigit() or '-->' in line:
                continue
            clean_line = re.sub(r"<[^>]+>", "", line).strip()  # 删除 HTML 标签
            if clean_line:  # 跳过空行
                cleaned_lines.append(clean_line)

        with open(txt_file_path, 'w', encoding='utf-8') as txt_file:
            txt_file.write(' '.join(cleaned_lines))

        print(f"已将 {srt_file_path} 转换为纯文字: {txt_file_path}")
    except Exception as e:
        print(f"转换 .srt 为 .txt 时出错: {e}")

def download_english_subtitle(video_url, channel_name, sanitized_title_full):
    """下载英文字幕 - 基于原逻辑，只改语言"""
    try:
        # 构建输出路径 - 原逻辑保持不变
        vtt_filename_template = f'【{channel_name}】{sanitized_title_full}.%(ext)s'
        vtt_file_path = os.path.join(VTT_HAN_FOLDER, vtt_filename_template)
        srt_filename = f'【{channel_name}】{sanitized_title_full}.en.srt'  # 先生成英文SRT
        srt_file_path = os.path.join(SRT_HAN_FOLDER, srt_filename)

        # 如果SRT文件已存在且大小合适，直接返回成功 - 原逻辑保持不变
        if check_subtitle_size(srt_file_path):
            return True, srt_file_path

        # 构建下载命令 - 原逻辑保持不变，只改语言
        command = [
            'yt-dlp',
            '--cookies-from-browser', 'firefox',
        ]
        if USE_PROXY:
            command += ['--proxy', PROXY_URL]
        command += [
            '--write-auto-sub',
            '--skip-download',
            '--sub-lang', 'en',  # 只改这里：zh-Hans → en
            '--sub-format', 'vtt',
            '--socket-timeout', '60',
            '-o', vtt_file_path,
            video_url
        ]

        print(f"下载英文字幕: {' '.join(command)}")

        # 执行下载 - 原逻辑保持不变
        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"字幕下载失败: {result.stderr}")
            return False, None

        # 检查VTT文件 - 原逻辑保持不变，只改文件名
        expected_vtt_path = vtt_file_path.replace('%(ext)s', 'en.vtt')  # zh-Hans.vtt → en.vtt
        if not check_subtitle_size(expected_vtt_path):
            return False, None

        # 转换为SRT格式 - 原逻辑完全保持不变
        convert_vtt_to_srt(expected_vtt_path, srt_file_path, max_length=26)
        
        # 最终检查SRT文件 - 原逻辑保持不变
        if check_subtitle_size(srt_file_path):
            return True, srt_file_path
            
        return False, None
    except Exception as e:
        print(f"下载字幕时出错: {e}")
        return False, None

def translate_english_srt_to_chinese(english_srt_path, chinese_srt_path):
    """新增：将英文SRT翻译为中文SRT"""
    try:
        print(f"开始翻译: {english_srt_path} → {chinese_srt_path}")
        
        with open(english_srt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        translated_lines = []
        
        for line in lines:
            line = line.rstrip('\n\r')
            
            # 保留序号、时间戳和空行
            if (line.strip().isdigit() or 
                '-->' in line or 
                line.strip() == ''):
                translated_lines.append(line)
            else:
                # 翻译字幕内容行
                try:
                    clean_text = re.sub(r'<[^>]+>', '', line)
                    if clean_text.strip():
                        translated_text = translator.translate(clean_text)
                        translated_lines.append(translated_text)
                        print(f"翻译: '{clean_text}' → '{translated_text}'")
                        time.sleep(0.2)  # 避免API限制
                    else:
                        translated_lines.append(line)
                except Exception as e:
                    print(f"翻译失败，保留原文: {line} (错误: {e})")
                    translated_lines.append(line)

        # 保存翻译后的中文SRT文件
        with open(chinese_srt_path, 'w', encoding='utf-8') as f:
            for line in translated_lines:
                f.write(line + '\n')

        print(f"翻译完成: {chinese_srt_path}")
        return True
        
    except Exception as e:
        print(f"翻译SRT文件时出错: {e}")
        return False

def process_english_subtitle_with_translation(video_url, channel_name, sanitized_title_full):
    """主处理函数：基于原逻辑，下载英文字幕并翻译为中文"""
    try:
        print("=== 开始处理英文字幕（基于原逻辑）===")
        
        # 步骤1: 下载英文字幕并转换为SRT（完全使用原逻辑）
        success, english_srt_path = download_english_subtitle(video_url, channel_name, sanitized_title_full)
        if not success:
            print("英文字幕下载失败")
            return False

        # 步骤2: 翻译英文SRT为中文SRT（新增步骤）
        chinese_srt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.srt'
        chinese_srt_path = os.path.join(SRT_HAN_FOLDER, chinese_srt_filename)
        
        if not translate_english_srt_to_chinese(english_srt_path, chinese_srt_path):
            print("SRT翻译失败")
            return False

        # 步骤3: 转换为TXT（原逻辑保持不变）
        txt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.txt'
        txt_file_path = os.path.join(VTT_HAN_FOLDER, txt_filename)
        convert_srt_to_txt(chinese_srt_path, txt_file_path)

        # 最终检查
        if check_subtitle_size(chinese_srt_path):
            print("=== 英文字幕翻译处理完成 ===")
            return True
        else:
            print("最终文件检查失败")
            return False

    except Exception as e:
        print(f"处理英文字幕翻译时出错: {e}")
        return False

def main():
    """测试主函数"""
    print("英文字幕下载+翻译测试（基于原逻辑）")
    print("=" * 50)
    
    # 测试视频
    video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    channel_name = "TestChannel"
    video_title = "Test Video"
    
    print(f"测试视频: {video_url}")
    print(f"频道: {channel_name}")
    print(f"标题: {video_title}")
    
    # 执行处理
    success = process_english_subtitle_with_translation(video_url, channel_name, video_title)
    
    if success:
        print("\n🎉 测试成功！")
        print("生成的文件:")
        print(f"   英文SRT: test_subtitles/【{channel_name}】{video_title}.en.srt")
        print(f"   中文SRT: test_subtitles/【{channel_name}】{video_title}.zh-Hans.srt")
        print(f"   中文TXT: test_subtitles/【{channel_name}】{video_title}.zh-Hans.txt")
    else:
        print("\n❌ 测试失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
