# YouTube视频下载系统 - 安装部署指南

本指南将帮助您在本地环境中成功安装和部署YouTube视频下载系统。

## 1. 系统要求

### 硬件要求
- 至少2GB RAM（推荐4GB以上）
- 至少20GB可用磁盘空间（用于存储下载的视频）
- 稳定的网络连接（推荐宽带连接）

### 软件要求
- Python 3.8或更高版本
- Firefox浏览器（用于获取cookies）
- ffmpeg（用于视频处理和元数据提取）
- aria2c（用于高效下载）

## 2. 环境准备

### 2.1 安装Python
1. 访问[Python官网](https://www.python.org/downloads/)下载并安装Python 3.8+
2. 安装时勾选"Add Python to PATH"选项
3. 验证安装：
   ```bash
   python --version
   pip --version
   ```

### 2.2 安装FFmpeg
#### Windows
1. 访问[FFmpeg官网](https://ffmpeg.org/download.html#build-windows)下载Windows版本
2. 解压到一个固定目录（如`C:\ffmpeg`）
3. 将`C:\ffmpeg\bin`添加到系统PATH环境变量
4. 验证安装：
   ```bash
   ffmpeg -version
   ffprobe -version
   ```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install ffmpeg
ffmpeg -version
```

### 2.3 安装aria2
#### Windows
1. 访问[aria2官网](https://github.com/aria2/aria2/releases)下载最新版本
2. 解压到一个固定目录（如`C:\aria2`）
3. 将`C:\aria2`添加到系统PATH环境变量
4. 验证安装：
   ```bash
   aria2c --version
   ```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install aria2
aria2c --version
```

### 2.4 安装Firefox浏览器
1. 访问[Firefox官网](https://www.mozilla.org/firefox/new/)下载并安装Firefox
2. 登录您的YouTube账号，确保可以访问需要监控的频道

## 3. 下载和安装项目

### 3.1 克隆或下载项目
```bash
# 使用git克隆（如果有git仓库）
git clone [项目仓库地址]

# 或者下载并解压项目ZIP文件到本地目录
```

### 3.2 安装Python依赖
进入项目目录，执行：
```bash
pip install -r requirements.txt
```

如果项目中没有`requirements.txt`文件，请手动安装以下依赖：
```bash
pip install httpx feedparser fuzzywuzzy pycaption python-dateutil pysrt
```

### 3.3 安装yt-dlp
```bash
pip install yt-dlp
```

验证安装：
```bash
yt-dlp --version
```

## 4. 配置系统

### 4.1 基本配置
编辑`config.py`文件，根据您的需求修改以下关键配置：

```python
# 设置下载路径（请确保路径存在或创建相应目录）
DOWNLOAD_PATHS = {
    "required": "D:/ytb_python_download/",  # 主下载路径，修改为您的首选路径
    "alternative": "D:/ytb_python_download/alternative"  # 备用下载路径
}

# 设置检查间隔（秒）
CHECK_INTERVAL = 3600  # 每小时检查一次，可根据需要调整

# 代理设置（如需使用代理）
USE_PROXY = False
PROXY_URL = "http://127.0.0.1:7890"  # 修改为您的代理地址
```

### 4.2 创建必要的目录
确保以下目录存在：
```bash
mkdir -p D:/ytb_python_download/vtt_han
mkdir -p D:/ytb_python_download/srt_han
mkdir -p D:/ytb_python_download/thumbnails
mkdir -p D:/ytb_python_download/timestamps
```

（请将路径替换为您在`config.py`中设置的实际路径）

### 4.3 配置监控频道
编辑`monitor.py`中的频道列表：
```python
channels = [
    # 请替换为您要监控的频道信息
    # 格式: [频道名称, 频道ID, 监控时间范围, 下载位置, 备注]
    ["Channel_Name_1", "UCXXXXXXXXXXXXXXXXXXXXXXXX", timedelta(days=7), "required", "Example Channel 1"],
    ["Channel_Name_2", "UCYYYYYYYYYYYYYYYYYYYYYYYY", timedelta(days=14), "alternative", "Example Channel 2"],
    # 添加更多频道...
]
```

#### 如何获取频道ID
1. 打开YouTube频道页面
2. 查看网页源代码，搜索"channelId"
3. 频道ID格式通常为"UC"开头的24个字符

### 4.4 配置关键词过滤（可选）
编辑`utils.py`中的关键词列表，或创建`keywords.txt`文件：
```
关键词1
关键词2
# 这是注释行
关键词3
```

## 5. 运行系统

### 5.1 启动系统
```bash
python main.py
```

建议使用后台运行或服务方式启动，特别是在服务器环境。

### 5.2 Windows后台运行
创建批处理文件`start_monitor.bat`：
```batch
@echo off
cd /d "项目路径"
start /MIN pythonw main.py
```

### 5.3 Linux后台运行
使用nohup或创建systemd服务：
```bash
nohup python main.py > youtube_download.log 2>&1 &
```

#### systemd服务配置
创建文件`/etc/systemd/system/youtube-download.service`：
```
[Unit]
Description=YouTube Video Downloader
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/project
ExecStart=/usr/bin/python main.py
Restart=on-failure
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用并启动服务：
```bash
sudo systemctl enable youtube-download.service
sudo systemctl start youtube-download.service
```

## 6. 验证与测试

### 6.1 测试单个视频下载
创建一个测试脚本`test_download.py`：
```python
from downloader import download_video
from video_info import get_video_info
import sys

def test_single_video(video_id):
    video_url = f"https://www.youtube.com/watch?v={video_id}"
    
    # 获取视频信息
    info = get_video_info(video_id)
    if info:
        video_title = info['title']
        channel_name = info['channel']
        print(f"找到视频: {video_title} (频道: {channel_name})")
        
        # 下载视频
        result = download_video(video_id, video_title, channel_name, "D:/ytb_python_download/", None)
        return result
    else:
        print(f"无法获取视频信息: {video_id}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        video_id = sys.argv[1]
        test_single_video(video_id)
    else:
        print("请提供视频ID作为参数")
```

运行测试：
```bash
python test_download.py dQw4w9WgXcQ  # 替换为您要测试的视频ID
```

### 6.2 检查日志文件
查看日志文件以确认系统正在按预期工作：
```bash
tail -f youtube_download.log  # Linux
# 或在Windows中直接打开日志文件
```

## 7. 常见安装问题

### 7.1 yt-dlp无法下载视频
**症状**: 出现"HTTP Error 429: Too Many Requests"错误
**解决方案**: 
1. 确保Firefox已登录YouTube
2. 尝试启用代理
3. 降低检查频率（增加`CHECK_INTERVAL`值）

### 7.2 找不到ffprobe命令
**症状**: 系统报错"找不到ffprobe命令"
**解决方案**: 确认ffmpeg已正确安装并添加到PATH，重启命令行或电脑

### 7.3 无法获取字幕
**症状**: 系统显示"无法下载字幕"
**解决方案**:
1. 检查视频是否有字幕
2. 确认网络连接
3. 检查代理设置

## 8. 更新与维护

### 8.1 更新yt-dlp
定期更新yt-dlp以确保与YouTube的兼容性：
```bash
pip install yt-dlp --upgrade
```

### 8.2 定期清理
设置定期清理任务以防止磁盘空间不足：
- 删除过时的日志文件
- 清理不需要的视频文件
- 检查和删除损坏的字幕文件

## 9. 安全注意事项

1. **API密钥保护**: 不要公开分享您的`config.py`文件，尤其是包含API密钥的版本
2. **权限控制**: 确保下载目录具有适当的访问权限
3. **网络安全**: 使用代理时确保代理服务器安全可靠 