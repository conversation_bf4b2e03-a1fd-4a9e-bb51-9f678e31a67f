#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版最终逻辑测试
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def split_long_text(text, max_length=41):
    """将超长文本按41字符限制拆分"""
    if len(text) <= max_length:
        return [text]
    
    parts = []
    while len(text) > max_length:
        # 尝试在合适的位置断开
        split_pos = max_length
        
        # 向前查找合适的断点（标点符号或空格）
        for i in range(max_length - 1, max(0, max_length - 10), -1):
            if text[i] in '，。！？；：、 ':
                split_pos = i + 1
                break
        
        parts.append(text[:split_pos].strip())
        text = text[split_pos:].strip()
    
    if text:
        parts.append(text)
    
    return parts

def test_simple_final():
    """简化版测试"""
    print("简化版最终逻辑测试")
    print("=" * 50)
    
    # 模拟几个句子的翻译和41字符限制检查
    test_sentences = [
        "[Music]",
        "We're no strangers to love. You know the rules and so do I.",
        "Never going to give you up. I'm going to let you down."
    ]
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n句子 {i}: '{sentence}'")
        
        # 翻译
        try:
            if sentence in ['[Music]', '[Applause]', '[Laughter]']:
                translation_map = {
                    '[Music]': '[音乐]',
                    '[Applause]': '[掌声]',
                    '[Laughter]': '[笑声]'
                }
                translated = translation_map.get(sentence, sentence)
                print(f"   特殊标记翻译: '{translated}'")
            else:
                translated = translator.translate(sentence)
                print(f"   翻译结果: '{translated}'")
                time.sleep(0.3)
            
            # 检查41字符限制
            print(f"   翻译长度: {len(translated)} 字符")
            if len(translated) > 41:
                print(f"   ⚠️  超过41字符限制，进行拆分")
                split_parts = split_long_text(translated, max_length=41)
                print(f"   拆分为 {len(split_parts)} 部分:")
                for j, part in enumerate(split_parts, 1):
                    print(f"     部分{j}: '{part}' ({len(part)} 字符)")
            else:
                print(f"   ✅ 符合41字符限制")
                
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
    
    print("\n✅ 简化版测试完成")
    
    # 测试拆分功能
    print("\n=== 测试41字符拆分功能 ===")
    long_text = "这是一个非常长的中文句子，用来测试41字符限制的拆分功能，看看是否能够正确地在合适的位置进行断句处理。"
    print(f"原文: '{long_text}' ({len(long_text)} 字符)")
    
    parts = split_long_text(long_text, max_length=41)
    print(f"拆分为 {len(parts)} 部分:")
    for i, part in enumerate(parts, 1):
        status = "✅" if len(part) <= 41 else "❌"
        print(f"  部分{i}: '{part}' ({len(part)} 字符) {status}")

if __name__ == "__main__":
    test_simple_final()
