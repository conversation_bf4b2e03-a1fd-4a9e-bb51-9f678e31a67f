#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的字幕翻译系统 - 集成Gemini和Google Translate
"""

import os
import re
import time
import requests
import json
from deep_translator import GoogleTranslator

class GeminiTranslator:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
    
    def translate(self, text):
        """使用Gemini翻译文本"""
        prompt = f"""请将以下英文翻译成中文，要求：
1. 翻译要自然流畅
2. 保持原文的语气和情感
3. 如果是歌词，保持韵律感
4. 只返回翻译结果，不要其他解释

英文原文：{text}

中文翻译："""

        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.3,
                "topK": 1,
                "topP": 1,
                "maxOutputTokens": 1024,
            }
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers=headers,
                data=json.dumps(payload),
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text']
                    content = content.strip()
                    # 清理可能的前缀
                    if content.startswith('中文翻译：'):
                        content = content[5:].strip()
                    elif content.startswith('翻译：'):
                        content = content[3:].strip()
                    return content
                else:
                    raise Exception("Gemini API返回格式错误")
            else:
                raise Exception(f"Gemini API错误: {response.status_code}")
                
        except Exception as e:
            raise Exception(f"Gemini翻译失败: {str(e)}")

class SubtitleTranslator:
    def __init__(self, gemini_api_key=None):
        self.gemini_api_key = gemini_api_key
        if gemini_api_key:
            self.gemini_translator = GeminiTranslator(gemini_api_key)
        self.google_translator = GoogleTranslator(source='en', target='zh-CN')
    
    def convert_vtt_to_srt_no_limit(self, vtt_file_path, srt_file_path):
        """VTT转SRT，不做字数限制，保持完整句子"""
        print(f"VTT转SRT: {vtt_file_path} -> {srt_file_path}")
        
        try:
            with open(vtt_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            cleaned_lines = []
            block = []
            index_counter = 1
            last_line_text = None

            for line in lines:
                line = line.strip()

                # 跳过头部信息
                if (not line or 
                    line.startswith("WEBVTT") or 
                    line.startswith("Kind:") or 
                    line.startswith("Language:")):
                    continue

                # 如果这是时间戳行
                if '-->' in line:
                    # 先把上一个block写入
                    if block and len(block) > 1:
                        content_lines = [content.strip() for content in block[1:] if content.strip()]
                        if content_lines:
                            cleaned_lines.append(str(index_counter))
                            cleaned_lines.append(block[0])  # 时间戳
                            for content in content_lines:
                                cleaned_lines.append(content)
                            cleaned_lines.append("")
                            index_counter += 1
                        
                    block = []

                    # 修正vtt->srt时间戳
                    line = line.replace('.', ',')
                    line = re.sub(r'\s*align:\w+|\s*position:\d+%', '', line).strip()
                    block.append(line)
                    
                else:
                    # 字幕内容行
                    text = re.sub(r'<[^>]+>', '', line)  # 去掉HTML标签
                    text = re.sub(r'<\d+:\d+:\d+\.\d+>', '', text)  # 去掉时间标记
                    text = text.strip()
                    
                    if text and text != last_line_text:
                        # 不做字数限制，保持完整内容
                        block.append(text)
                        last_line_text = text

            # 处理最后一个block
            if block and len(block) > 1:
                content_lines = [content.strip() for content in block[1:] if content.strip()]
                if content_lines:
                    cleaned_lines.append(str(index_counter))
                    cleaned_lines.append(block[0])
                    for content in content_lines:
                        cleaned_lines.append(content)
                    cleaned_lines.append("")

            # 写入SRT文件
            with open(srt_file_path, 'w', encoding='utf-8') as sf:
                for cl in cleaned_lines:
                    sf.write(cl + '\n')

            print(f"✅ VTT转SRT完成，生成 {index_counter - 1} 个字幕块")
            return True
            
        except Exception as e:
            print(f"❌ VTT转SRT出错: {e}")
            return False
    
    def check_file_has_punctuation(self, srt_content):
        """检查整个SRT文件是否包含标点符号"""
        print("检查文件标点符号...")
        
        # 提取所有字幕内容
        subtitle_content = ""
        lines = srt_content.split('\n')
        
        for line in lines:
            line = line.strip()
            if (line.isdigit() or '-->' in line or not line):
                continue
            subtitle_content += line + " "
        
        # 检查标点符号
        punctuation_matches = re.findall(r'[.!?,:;]', subtitle_content)
        
        print(f"   字幕内容长度: {len(subtitle_content)} 字符")
        print(f"   标点符号数量: {len(punctuation_matches)}")
        
        if len(punctuation_matches) == 0:
            print("❌ 没有标点符号，字幕质量有问题")
            return False
        else:
            print(f"✅ 有 {len(punctuation_matches)} 个标点符号，可以翻译")
            return True
    
    def extract_complete_sentences(self, srt_content):
        """提取完整句子"""
        print("提取完整句子...")
        
        # 提取所有字幕内容
        subtitle_lines = []
        lines = srt_content.split('\n')
        
        for line in lines:
            line = line.strip()
            if (line.isdigit() or '-->' in line or not line):
                continue
            subtitle_lines.append(line)
        
        # 合并所有内容
        full_text = ' '.join(subtitle_lines)
        print(f"   合并文本长度: {len(full_text)} 字符")
        
        # 按句号分割句子
        sentences = re.split(r'(?<=[.!?])\s+', full_text.strip())
        sentences = [s.strip() for s in sentences if s.strip()]
        
        print(f"   分割出 {len(sentences)} 个句子")
        return sentences
    
    def split_long_text(self, text, max_length=41):
        """翻译后检查41字符限制，超过才拆分"""
        if len(text) <= max_length:
            return [text]
        
        parts = []
        while len(text) > max_length:
            split_pos = max_length
            
            # 向前查找合适的断点
            for i in range(max_length - 1, max(0, max_length - 10), -1):
                if text[i] in '，。！？；：、 ':
                    split_pos = i + 1
                    break
            
            parts.append(text[:split_pos].strip())
            text = text[split_pos:].strip()
        
        if text:
            parts.append(text)
        
        return parts
    
    def translate_sentence(self, sentence, use_gemini=True):
        """翻译单个句子，优先使用Gemini，失败时使用Google"""
        # 特殊标记处理
        if sentence in ['[Music]', '[Applause]', '[Laughter]']:
            translation_map = {
                '[Music]': '[音乐]',
                '[Applause]': '[掌声]',
                '[Laughter]': '[笑声]'
            }
            return translation_map.get(sentence, sentence)
        
        # 尝试Gemini翻译
        if use_gemini and self.gemini_api_key:
            try:
                return self.gemini_translator.translate(sentence)
            except Exception as e:
                print(f"   Gemini翻译失败: {e}")
                print(f"   降级到Google Translate")
        
        # 使用Google Translate
        try:
            return self.google_translator.translate(sentence)
        except Exception as e:
            print(f"   Google翻译也失败: {e}")
            return sentence  # 返回原文
    
    def translate_all_sentences(self, sentences, use_gemini=True):
        """翻译所有句子"""
        print(f"开始翻译所有 {len(sentences)} 个句子...")
        print(f"主要翻译引擎: {'Gemini' if use_gemini and self.gemini_api_key else 'Google Translate'}")
        print("=" * 50)
        
        results = []
        success_count = 0
        gemini_count = 0
        google_count = 0
        split_count = 0
        
        for i, sentence in enumerate(sentences, 1):
            print(f"\n[{i}/{len(sentences)}] '{sentence[:60]}{'...' if len(sentence) > 60 else ''}'")
            
            try:
                # 翻译句子
                if use_gemini and self.gemini_api_key:
                    try:
                        translated = self.gemini_translator.translate(sentence)
                        gemini_count += 1
                        print(f"   Gemini: '{translated}' ({len(translated)}字符)")
                    except Exception as e:
                        print(f"   Gemini失败，使用Google: {e}")
                        translated = self.google_translator.translate(sentence)
                        google_count += 1
                        print(f"   Google: '{translated}' ({len(translated)}字符)")
                else:
                    translated = self.google_translator.translate(sentence)
                    google_count += 1
                    print(f"   Google: '{translated}' ({len(translated)}字符)")
                
                # 翻译后检查41字符限制
                if len(translated) > 41:
                    print(f"   ⚠️  超过41字符，拆分")
                    split_parts = self.split_long_text(translated, max_length=41)
                    print(f"   拆分为: {split_parts}")
                    split_count += 1
                    
                    results.append({
                        'original': sentence,
                        'translated': translated,
                        'split_parts': split_parts,
                        'needs_split': True
                    })
                else:
                    results.append({
                        'original': sentence,
                        'translated': translated,
                        'split_parts': [translated],
                        'needs_split': False
                    })
                
                success_count += 1
                time.sleep(0.2)  # 避免API限制
                    
            except Exception as e:
                print(f"   ❌ 翻译失败: {e}")
                results.append({
                    'original': sentence,
                    'translated': sentence,
                    'split_parts': [sentence],
                    'needs_split': False,
                    'error': str(e)
                })
        
        print(f"\n" + "=" * 50)
        print(f"翻译完成统计:")
        print(f"   总句子数: {len(sentences)}")
        print(f"   翻译成功: {success_count}")
        print(f"   Gemini翻译: {gemini_count}")
        print(f"   Google翻译: {google_count}")
        print(f"   需要拆分: {split_count}")
        
        return results

    def generate_complete_chinese_srt(self, results, original_srt_content):
        """生成完整的中文SRT文件"""
        print(f"\n生成完整中文SRT文件...")

        # 解析原始SRT的时间戳
        original_blocks = []
        srt_blocks = original_srt_content.strip().split('\n\n')

        for block in srt_blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                try:
                    index = int(lines[0])
                    timestamp = lines[1]
                    original_blocks.append({
                        'index': index,
                        'timestamp': timestamp
                    })
                except ValueError:
                    continue

        print(f"   原始时间戳块数: {len(original_blocks)}")

        # 生成中文SRT
        chinese_lines = []
        index = 1
        block_index = 0

        for result in results:
            for part in result['split_parts']:
                if block_index < len(original_blocks):
                    timestamp = original_blocks[block_index]['timestamp']
                else:
                    # 如果时间戳不够，使用默认时间戳
                    timestamp = "00:00:00,000 --> 00:00:03,000"

                chinese_lines.append(str(index))
                chinese_lines.append(timestamp)
                chinese_lines.append(part)
                chinese_lines.append("")

                index += 1
                block_index += 1

        print(f"   生成中文字幕块数: {index - 1}")
        return '\n'.join(chinese_lines)

    def translate_subtitle_file(self, vtt_file_path, output_srt_path, use_gemini=True):
        """完整的字幕翻译流程"""
        print("完整字幕翻译流程")
        print("=" * 60)

        # 步骤1: VTT转SRT (不限制字数)
        temp_srt_path = vtt_file_path.replace('.vtt', '_temp.srt')
        if not self.convert_vtt_to_srt_no_limit(vtt_file_path, temp_srt_path):
            return False

        # 步骤2: 读取SRT文件
        with open(temp_srt_path, 'r', encoding='utf-8') as f:
            srt_content = f.read()

        # 步骤3: 检查标点符号
        if not self.check_file_has_punctuation(srt_content):
            print("字幕质量检查失败")
            return False

        # 步骤4: 提取完整句子
        sentences = self.extract_complete_sentences(srt_content)

        # 步骤5: 翻译所有句子
        results = self.translate_all_sentences(sentences, use_gemini=use_gemini)

        # 步骤6: 生成最终中文SRT
        chinese_srt_content = self.generate_complete_chinese_srt(results, srt_content)

        # 步骤7: 保存文件
        with open(output_srt_path, 'w', encoding='utf-8') as f:
            f.write(chinese_srt_content)

        print(f"\n✅ 字幕翻译完成")
        print(f"   输出文件: {output_srt_path}")
        print(f"   文件大小: {os.path.getsize(output_srt_path)} 字节")

        # 清理临时文件
        try:
            os.remove(temp_srt_path)
        except:
            pass

        return True

def test_complete_subtitle_translation():
    """测试完整的字幕翻译系统"""
    print("完整字幕翻译系统测试")
    print("=" * 60)

    # 使用Gemini API密钥
    gemini_api_key = "AIzaSyBrEmZW9wqmyil7xPsbK2fgMb7LjII8NlQ"

    # 初始化翻译器
    translator = SubtitleTranslator(gemini_api_key=gemini_api_key)

    # 输入和输出文件
    vtt_file = "complete_english.en.vtt"
    output_srt = "complete_english_gemini.zh-Hans.srt"

    if not os.path.exists(vtt_file):
        print(f"❌ VTT文件不存在: {vtt_file}")
        return False

    print(f"✅ 找到VTT文件: {vtt_file}")
    print(f"   文件大小: {os.path.getsize(vtt_file)} 字节")

    # 执行完整翻译流程
    success = translator.translate_subtitle_file(
        vtt_file_path=vtt_file,
        output_srt_path=output_srt,
        use_gemini=True
    )

    if success:
        print(f"\n🎉 完整翻译系统测试成功！")

        # 显示最终文件的一部分
        with open(output_srt, 'r', encoding='utf-8') as f:
            content = f.read()

        print(f"\n最终中文SRT文件前20行:")
        lines = content.split('\n')[:20]
        for i, line in enumerate(lines, 1):
            print(f"   {i:2d}: {line}")

        return True
    else:
        print(f"\n❌ 翻译系统测试失败")
        return False

if __name__ == "__main__":
    test_complete_subtitle_translation()
