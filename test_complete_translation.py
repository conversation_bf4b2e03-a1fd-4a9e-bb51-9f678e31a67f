#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整翻译整个字幕文件，检查翻译效果
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def extract_complete_sentences(srt_content):
    """提取完整句子"""
    print("提取完整句子...")
    
    # 提取所有字幕内容
    subtitle_lines = []
    lines = srt_content.split('\n')
    
    for line in lines:
        line = line.strip()
        if (line.isdigit() or '-->' in line or not line):
            continue
        subtitle_lines.append(line)
    
    # 合并所有内容
    full_text = ' '.join(subtitle_lines)
    print(f"   合并文本长度: {len(full_text)} 字符")
    
    # 按句号分割句子
    sentences = re.split(r'(?<=[.!?])\s+', full_text.strip())
    sentences = [s.strip() for s in sentences if s.strip()]
    
    print(f"   分割出 {len(sentences)} 个句子")
    return sentences

def split_long_text(text, max_length=41):
    """翻译后检查41字符限制，超过才拆分"""
    if len(text) <= max_length:
        return [text]
    
    parts = []
    while len(text) > max_length:
        split_pos = max_length
        
        # 向前查找合适的断点
        for i in range(max_length - 1, max(0, max_length - 10), -1):
            if text[i] in '，。！？；：、 ':
                split_pos = i + 1
                break
        
        parts.append(text[:split_pos].strip())
        text = text[split_pos:].strip()
    
    if text:
        parts.append(text)
    
    return parts

def translate_all_sentences(sentences):
    """翻译所有句子"""
    print(f"开始翻译所有 {len(sentences)} 个句子...")
    print("=" * 50)
    
    results = []
    success_count = 0
    error_count = 0
    split_count = 0
    
    for i, sentence in enumerate(sentences, 1):
        print(f"\n[{i}/{len(sentences)}] '{sentence[:60]}{'...' if len(sentence) > 60 else ''}'")
        
        try:
            # 翻译
            if sentence in ['[Music]', '[Applause]', '[Laughter]']:
                translation_map = {
                    '[Music]': '[音乐]',
                    '[Applause]': '[掌声]',
                    '[Laughter]': '[笑声]'
                }
                translated = translation_map.get(sentence, sentence)
                print(f"   特殊标记: '{translated}'")
            else:
                translated = translator.translate(sentence)
                print(f"   翻译: '{translated}' ({len(translated)}字符)")
                time.sleep(0.2)  # 避免API限制
            
            # 翻译后检查41字符限制
            if len(translated) > 41:
                print(f"   ⚠️  超过41字符，拆分")
                split_parts = split_long_text(translated, max_length=41)
                print(f"   拆分为: {split_parts}")
                split_count += 1
                
                results.append({
                    'original': sentence,
                    'translated': translated,
                    'split_parts': split_parts,
                    'needs_split': True
                })
            else:
                results.append({
                    'original': sentence,
                    'translated': translated,
                    'split_parts': [translated],
                    'needs_split': False
                })
            
            success_count += 1
                
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
            error_count += 1
            results.append({
                'original': sentence,
                'translated': sentence,
                'split_parts': [sentence],
                'needs_split': False,
                'error': str(e)
            })
            
            # 如果连续失败太多，可能是API问题
            if error_count > 5:
                print(f"\n⚠️  连续翻译失败过多，可能是API问题，暂停5秒...")
                time.sleep(5)
    
    print(f"\n" + "=" * 50)
    print(f"翻译完成统计:")
    print(f"   总句子数: {len(sentences)}")
    print(f"   翻译成功: {success_count}")
    print(f"   翻译失败: {error_count}")
    print(f"   需要拆分: {split_count}")
    
    return results

def generate_complete_chinese_srt(results, original_srt_content):
    """生成完整的中文SRT文件"""
    print(f"\n生成完整中文SRT文件...")
    
    # 解析原始SRT的时间戳
    original_blocks = []
    srt_blocks = original_srt_content.strip().split('\n\n')
    
    for block in srt_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            try:
                index = int(lines[0])
                timestamp = lines[1]
                original_blocks.append({
                    'index': index,
                    'timestamp': timestamp
                })
            except ValueError:
                continue
    
    print(f"   原始时间戳块数: {len(original_blocks)}")
    
    # 生成中文SRT
    chinese_lines = []
    index = 1
    block_index = 0
    
    for result in results:
        for part in result['split_parts']:
            if block_index < len(original_blocks):
                timestamp = original_blocks[block_index]['timestamp']
            else:
                # 如果时间戳不够，使用默认时间戳
                timestamp = "00:00:00,000 --> 00:00:03,000"
            
            chinese_lines.append(str(index))
            chinese_lines.append(timestamp)
            chinese_lines.append(part)
            chinese_lines.append("")
            
            index += 1
            block_index += 1
    
    print(f"   生成中文字幕块数: {index - 1}")
    return '\n'.join(chinese_lines)

def analyze_translation_quality(results):
    """分析翻译质量"""
    print(f"\n" + "=" * 50)
    print("翻译质量分析:")
    
    # 统计字符长度分布
    lengths = [len(r['translated']) for r in results if 'error' not in r]
    if lengths:
        avg_length = sum(lengths) / len(lengths)
        max_length = max(lengths)
        min_length = min(lengths)
        
        print(f"   平均长度: {avg_length:.1f} 字符")
        print(f"   最长: {max_length} 字符")
        print(f"   最短: {min_length} 字符")
        
        # 长度分布
        short_count = sum(1 for l in lengths if l <= 20)
        medium_count = sum(1 for l in lengths if 20 < l <= 41)
        long_count = sum(1 for l in lengths if l > 41)
        
        print(f"   长度分布:")
        print(f"     短句(≤20字符): {short_count}")
        print(f"     中句(21-41字符): {medium_count}")
        print(f"     长句(>41字符): {long_count}")
    
    # 显示一些翻译示例
    print(f"\n翻译示例 (前10个):")
    for i, result in enumerate(results[:10], 1):
        if 'error' not in result:
            original = result['original'][:40] + ('...' if len(result['original']) > 40 else '')
            translated = result['translated']
            print(f"   {i:2d}. '{original}' → '{translated}'")
    
    # 显示需要拆分的句子
    split_results = [r for r in results if r['needs_split']]
    if split_results:
        print(f"\n需要拆分的句子 (共{len(split_results)}个):")
        for i, result in enumerate(split_results[:5], 1):
            print(f"   {i}. 原文: '{result['original'][:50]}...'")
            print(f"      译文: '{result['translated']}' ({len(result['translated'])}字符)")
            print(f"      拆分: {result['split_parts']}")

def test_complete_translation():
    """完整翻译测试"""
    print("完整字幕翻译测试")
    print("=" * 60)
    
    srt_file = "complete_english.en.srt"
    chinese_srt_file = "complete_english_full.zh-Hans.srt"
    
    if not os.path.exists(srt_file):
        print(f"❌ SRT文件不存在: {srt_file}")
        return False
    
    print(f"✅ 找到SRT文件: {srt_file}")
    
    # 读取SRT文件
    with open(srt_file, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    print(f"   文件大小: {len(srt_content)} 字符")
    
    # 提取完整句子
    sentences = extract_complete_sentences(srt_content)
    
    # 翻译所有句子
    results = translate_all_sentences(sentences)
    
    # 生成完整中文SRT
    chinese_srt_content = generate_complete_chinese_srt(results, srt_content)
    
    # 保存文件
    with open(chinese_srt_file, 'w', encoding='utf-8') as f:
        f.write(chinese_srt_content)
    
    print(f"\n✅ 完整翻译完成")
    print(f"   输出文件: {chinese_srt_file}")
    print(f"   文件大小: {os.path.getsize(chinese_srt_file)} 字节")
    
    # 分析翻译质量
    analyze_translation_quality(results)
    
    # 显示最终文件的一部分
    print(f"\n最终中文SRT文件前20行:")
    lines = chinese_srt_content.split('\n')[:20]
    for i, line in enumerate(lines, 1):
        print(f"   {i:2d}: {line}")
    
    return True

if __name__ == "__main__":
    test_complete_translation()
