#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
只测试VTT转换改进
"""

import os
import re

def should_skip_line(line):
    """检查是否需要跳过当前行（无用信息）"""
    if (
        not line  # 空行
        or line.startswith("WEBVTT")  # VTT 头部
        or line.startswith("Kind:")  # 字幕类型
        or line.startswith("Language:")  # 语言信息
    ):
        return True
    return False

def split_long_lines_to_time_blocks(text, max_length=26):
    """将长字幕拆分为多个时间块"""
    split_texts = []
    while len(text) > max_length:
        split_texts.append(text[:max_length])
        text = text[max_length:]
    split_texts.append(text)
    return split_texts

def convert_vtt_to_srt_improved(vtt_file_path, srt_file_path, max_length=26):
    """
    改进的VTT转SRT - 修复空字幕块问题
    """
    print(f"转换: {vtt_file_path} -> {srt_file_path}")
    print(f"最大字数限制: {max_length}")
    
    try:
        if not os.path.exists(vtt_file_path):
            print(f"❌ VTT文件不存在: {vtt_file_path}")
            return False

        with open(vtt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        print(f"原始VTT文件: {len(lines)} 行")

        cleaned_lines = []
        block = []
        index_counter = 1
        last_line_text = None
        empty_blocks = 0
        duplicate_count = 0
        split_count = 0

        for line_num, line in enumerate(lines, 1):
            line = line.strip()

            # 跳过头部信息
            if should_skip_line(line):
                continue

            # 如果这是时间戳行
            if '-->' in line:
                # 先把上一个block写入（如果有内容）
                if block and len(block) > 1:  # 确保有时间戳和内容
                    # 检查是否有实际的字幕内容
                    content_lines = [content.strip() for content in block[1:] if content.strip()]
                    if content_lines:  # 有实际内容
                        cleaned_lines.append(str(index_counter))
                        cleaned_lines.append(block[0])  # 时间戳
                        for content in content_lines:
                            cleaned_lines.append(content)
                        cleaned_lines.append("")  # 添加空行
                        index_counter += 1
                    else:
                        empty_blocks += 1
                        print(f"   跳过空字幕块: {block[0]}")
                    
                block = []  # 清空block

                # 修正vtt->srt时间戳
                line = line.replace('.', ',')  # 00:00:01.000 -> 00:00:01,000
                # 移除多余的 align: start position:0% 之类
                line = re.sub(r'\s*align:\w+|\s*position:\d+%', '', line).strip()
                block.append(line)
                
            else:
                # 非时间戳行(字幕内容)
                # 去掉HTML标签和时间标记
                text = re.sub(r'<[^>]+>', '', line)  # 去掉HTML标签
                text = re.sub(r'<\d+:\d+:\d+\.\d+>', '', text)  # 去掉时间标记
                text = text.strip()
                
                if text and text != last_line_text:
                    # 检查字数限制并拆分
                    if len(text) > max_length:
                        print(f"   长字幕拆分: '{text}' (长度: {len(text)})")
                        split_texts = split_long_lines_to_time_blocks(text, max_length)
                        block.extend(split_texts)
                        split_count += 1
                        print(f"   拆分为: {split_texts}")
                    else:
                        block.append(text)
                    last_line_text = text
                elif text == last_line_text:
                    duplicate_count += 1
                    print(f"   去重: 跳过重复字幕 '{text}'")

        # 处理最后一个block
        if block and len(block) > 1:
            content_lines = [content.strip() for content in block[1:] if content.strip()]
            if content_lines:
                cleaned_lines.append(str(index_counter))
                cleaned_lines.append(block[0])
                for content in content_lines:
                    cleaned_lines.append(content)
                cleaned_lines.append("")
            else:
                empty_blocks += 1

        # 写到 srt
        with open(srt_file_path, 'w', encoding='utf-8') as sf:
            for cl in cleaned_lines:
                sf.write(cl + '\n')

        print(f"✅ 转换完成")
        print(f"   有效字幕块: {index_counter - 1}")
        print(f"   跳过空块: {empty_blocks}")
        print(f"   去重数量: {duplicate_count}")
        print(f"   拆分数量: {split_count}")
        print(f"   输出文件大小: {os.path.getsize(srt_file_path)} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换出错: {e}")
        return False

def verify_srt_format(srt_file_path, max_length=26):
    """验证SRT格式"""
    print(f"\n=== 验证SRT格式 ===")
    
    try:
        with open(srt_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按空行分割字幕块
        blocks = content.strip().split('\n\n')
        
        print(f"总字幕块数: {len(blocks)}")
        
        format_errors = 0
        length_errors = 0
        
        # 检查前10个块
        for i, block in enumerate(blocks[:10], 1):
            lines = block.strip().split('\n')
            print(f"\n字幕块 {i}:")
            
            if len(lines) < 3:
                print(f"   ❌ 格式错误: 行数不足 ({len(lines)} < 3)")
                format_errors += 1
                continue
            
            # 检查序号
            if not lines[0].isdigit():
                print(f"   ❌ 序号错误: '{lines[0]}'")
                format_errors += 1
            else:
                print(f"   ✅ 序号: {lines[0]}")
            
            # 检查时间戳
            if '-->' not in lines[1]:
                print(f"   ❌ 时间戳错误: '{lines[1]}'")
                format_errors += 1
            else:
                print(f"   ✅ 时间戳: {lines[1]}")
            
            # 检查字幕内容和字数
            subtitle_lines = lines[2:]
            for j, subtitle_line in enumerate(subtitle_lines):
                text_length = len(subtitle_line)
                print(f"   ✅ 内容{j+1}: '{subtitle_line}' (长度: {text_length})")
                
                if text_length > max_length:
                    print(f"   ⚠️  超长警告: {text_length} > {max_length}")
                    length_errors += 1
        
        print(f"\n验证结果:")
        print(f"   格式错误: {format_errors}")
        print(f"   超长字幕: {length_errors}")
        
        return format_errors == 0 and length_errors == 0
        
    except Exception as e:
        print(f"❌ 验证出错: {e}")
        return False

def main():
    """主测试函数"""
    print("VTT转换改进测试")
    print("=" * 40)
    
    vtt_file = "real_test.en.vtt"
    srt_file = "real_test_improved.en.srt"
    
    if not os.path.exists(vtt_file):
        print(f"❌ VTT文件不存在: {vtt_file}")
        print("请先运行: yt-dlp --write-auto-sub --skip-download --sub-lang en --sub-format vtt -o \"real_test.%(ext)s\" \"https://www.youtube.com/watch?v=dQw4w9WgXcQ\"")
        return 1
    
    # 测试改进的转换
    if not convert_vtt_to_srt_improved(vtt_file, srt_file, max_length=26):
        print("❌ VTT转换失败")
        return 1
    
    # 验证格式
    if verify_srt_format(srt_file, max_length=26):
        print("\n🎉 VTT转换改进测试成功！")
        print(f"生成文件: {srt_file}")
        return 0
    else:
        print("\n⚠️  格式验证有问题，但转换基本成功")
        return 0

if __name__ == "__main__":
    exit(main())
