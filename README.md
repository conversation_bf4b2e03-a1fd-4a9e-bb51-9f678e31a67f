# YouTube视频下载模块

这个项目包含了一组用于下载YouTube视频的Python模块，主要功能包括：
- 从RSS订阅源获取频道最新视频
- 自动翻译视频标题
- 下载视频和字幕
- 处理字幕格式转换
- 保存下载历史

## 模块结构

- `config.py`: 配置参数和常量
- `utils.py`: 通用工具函数
- `translation.py`: 标题翻译功能
- `video_info.py`: 获取视频信息
- `subtitle.py`: 字幕下载和处理
- `downloader.py`: 视频下载功能
- `monitor.py`: 频道监控逻辑
- `main.py`: 主程序入口

## 使用方法

1. 确保已安装所需依赖：
   ```
   pip install feedparser httpx googletrans==4.0.0-rc1
   ```

2. 安装yt-dlp工具（用于视频下载）

3. 配置频道文件 `channels_videos_test.txt`，格式：
   ```
   频道名称 频道ID 检查时间(小时) 下载类型 额外信息
   ```

4. 运行程序：
   ```
   python main.py
   ```

## 配置文件

程序使用以下配置文件：
- `channels_videos_test.txt`: 要监控的频道列表
- `keywords.txt`: 过滤关键词
- `downloaded_videos.txt`: 已下载视频历史记录 