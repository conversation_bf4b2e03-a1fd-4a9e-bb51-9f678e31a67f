import os
import re
import time
import subprocess
from config import logger, USE_PROXY, PROXY_URL, VTT_HAN_FOLDER, SRT_HAN_FOLDER
from utils import sanitize_filename
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def check_subtitle_size(file_path, min_size_kb=1):
    """
    检查字幕文件是否存在且大小是否符合要求
    
    参数:
    - file_path: 字幕文件路径
    - min_size_kb: 最小文件大小（KB）
    
    返回:
    - bool: 如果文件存在且大小符合要求返回True，否则返回False
    """
    try:
        if not os.path.exists(file_path):
            logger.warning(f"字幕文件不存在: {file_path}")
            return False
            
        file_size_kb = os.path.getsize(file_path) / 1024  # 转换为KB
        if file_size_kb < min_size_kb:
            logger.warning(f"字幕文件大小不足 {min_size_kb}KB: {file_path} (当前大小: {file_size_kb:.2f}KB)")
            return False
            
        logger.warning(f"字幕文件检查通过: {file_path} (大小: {file_size_kb:.2f}KB)")
        return True
    except Exception as e:
        logger.error(f"检查字幕文件时出错: {e}")
        return False

def translate_subtitle_text(text):
    """
    使用谷歌翻译将英文字幕翻译为中文
    
    参数:
    - text: 英文字幕文本
    
    返回:
    - str: 翻译后的中文文本
    """
    try:
        if not text or not text.strip():
            return text
            
        # 移除HTML标签
        clean_text = re.sub(r'<[^>]+>', '', text)
        
        if not clean_text.strip():
            return text
            
        # 翻译文本
        translated = translator.translate(clean_text)
        logger.warning(f"翻译: '{clean_text}' -> '{translated}'")
        return translated
    except Exception as e:
        logger.error(f"翻译字幕文本时出错: {e}")
        return text  # 翻译失败时返回原文

def download_english_subtitle(video_url, channel_name, sanitized_title_full):
    """
    下载英文字幕并返回下载状态和字幕文件路径
    
    返回:
    - tuple: (是否成功, 英文VTT文件路径)
    """
    try:
        # 构建输出路径 - 先下载英文字幕
        vtt_filename_template = f'【{channel_name}】{sanitized_title_full}.%(ext)s'
        vtt_file_path = os.path.join(VTT_HAN_FOLDER, vtt_filename_template)

        # 构建下载命令 - 下载英文字幕
        command = [
            'yt-dlp',
            '--cookies-from-browser', 'firefox',
        ]
        if USE_PROXY:
            command += ['--proxy', PROXY_URL]
        command += [
            '--write-auto-sub',
            '--skip-download',
            '--sub-lang', 'en',  # 下载英文字幕
            '--sub-format', 'vtt',
            '--socket-timeout', '60',
            '-o', vtt_file_path,
            video_url
        ]

        logger.warning(f"开始下载英文字幕: {' '.join(command)}")
        
        # 执行下载
        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode != 0:
            logger.error(f"英文字幕下载失败: {result.stderr}")
            return False, None

        # 检查英文VTT文件
        expected_vtt_path = vtt_file_path.replace('%(ext)s', 'en.vtt')
        if not check_subtitle_size(expected_vtt_path):
            logger.error(f"英文字幕文件未生成或文件过小: {expected_vtt_path}")
            return False, None

        logger.warning(f"英文字幕下载成功: {expected_vtt_path}")
        return True, expected_vtt_path
        
    except Exception as e:
        logger.error(f"下载英文字幕时出错: {e}")
        return False, None

def translate_vtt_file(english_vtt_path, chinese_vtt_path):
    """
    将英文VTT文件翻译为中文VTT文件
    
    参数:
    - english_vtt_path: 英文VTT文件路径
    - chinese_vtt_path: 中文VTT文件路径
    
    返回:
    - bool: 翻译是否成功
    """
    try:
        if not os.path.exists(english_vtt_path):
            logger.error(f"英文VTT文件不存在: {english_vtt_path}")
            return False

        with open(english_vtt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        translated_lines = []
        
        for line in lines:
            line = line.rstrip('\n\r')
            
            # 保留VTT头部和时间戳行
            if (line.startswith('WEBVTT') or 
                line.startswith('Kind:') or 
                line.startswith('Language:') or
                '-->' in line or
                line.strip() == ''):
                translated_lines.append(line)
            else:
                # 翻译字幕内容行
                translated_text = translate_subtitle_text(line)
                translated_lines.append(translated_text)
                
                # 添加小延迟避免API限制
                time.sleep(0.1)

        # 保存翻译后的中文VTT文件
        with open(chinese_vtt_path, 'w', encoding='utf-8') as f:
            for line in translated_lines:
                f.write(line + '\n')

        logger.warning(f"VTT翻译完成: {english_vtt_path} -> {chinese_vtt_path}")
        return True
        
    except Exception as e:
        logger.error(f"翻译VTT文件时出错: {e}")
        return False

def process_english_subtitle_with_translation(video_url, channel_name, sanitized_title_full):
    """
    主处理函数：下载英文字幕，翻译为中文，然后转换为SRT和TXT格式
    
    返回:
    - bool: 处理是否成功
    """
    try:
        # 生成文件路径
        chinese_vtt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.vtt'
        chinese_vtt_path = os.path.join(VTT_HAN_FOLDER, chinese_vtt_filename)
        
        srt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.srt'
        srt_file_path = os.path.join(SRT_HAN_FOLDER, srt_filename)
        
        txt_filename = f'【{channel_name}】{sanitized_title_full}.zh-Hans.txt'
        txt_file_path = os.path.join(VTT_HAN_FOLDER, txt_filename)

        # 如果SRT文件已存在，则跳过处理
        if check_subtitle_size(srt_file_path):
            logger.warning(f"中文字幕文件 {srt_file_path} 已存在，跳过处理。")
            return True

        # 步骤1: 下载英文字幕
        logger.warning("步骤1: 下载英文字幕...")
        success, english_vtt_path = download_english_subtitle(video_url, channel_name, sanitized_title_full)
        if not success:
            logger.error("英文字幕下载失败")
            return False

        # 步骤2: 翻译英文VTT为中文VTT
        logger.warning("步骤2: 翻译英文字幕为中文...")
        if not translate_vtt_file(english_vtt_path, chinese_vtt_path):
            logger.error("字幕翻译失败")
            return False

        # 步骤3: 转换中文VTT为SRT
        logger.warning("步骤3: 转换VTT为SRT...")
        convert_vtt_to_srt(chinese_vtt_path, srt_file_path, max_length=26)

        # 步骤4: 转换SRT为TXT
        logger.warning("步骤4: 转换SRT为TXT...")
        convert_srt_to_txt(srt_file_path, txt_file_path)

        # 最终检查
        if check_subtitle_size(srt_file_path):
            logger.warning("英文字幕翻译处理完成！")
            
            # 清理临时英文VTT文件
            try:
                if os.path.exists(english_vtt_path):
                    os.remove(english_vtt_path)
                    logger.warning(f"已清理临时英文字幕文件: {english_vtt_path}")
            except Exception as e:
                logger.warning(f"清理临时文件时出错: {e}")
                
            return True
        else:
            logger.error("最终字幕文件检查失败")
            return False

    except Exception as e:
        logger.error(f"处理英文字幕翻译时出错: {e}")
        return False

def should_skip_line(line):
    """
    检查是否需要跳过当前行（无用信息）。
    """
    if (
        not line  # 空行
        or line.startswith("WEBVTT")  # VTT 头部
        or line.startswith("Kind:")  # 字幕类型
        or line.startswith("Language:")  # 语言信息
    ):
        return True
    return False

def convert_vtt_to_srt(vtt_file_path, srt_file_path, max_length=50):
    """
    将 .vtt 转成标准 .srt，同时处理长字幕行:
      - 识别时间戳 "00:01:00.000 --> 00:01:05.000"
      - 移除vtt头、空行
      - 将行长度超过max_length就拆分
    """
    try:
        if not os.path.exists(vtt_file_path):
            logger.error(f"VTT 不存在: {vtt_file_path}")
            return

        with open(vtt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        cleaned_lines = []
        block = []
        index_counter = 1
        last_line_text = None

        for line in lines:
            line = line.strip()

            # 跳过头部 "WEBVTT"等
            if should_skip_line(line):
                continue

            # 如果这是时间戳行
            if '-->' in line:
                # 先把上一个block写入
                if block:
                    cleaned_lines.append(str(index_counter))
                    cleaned_lines.append(block[0])  # 时间戳
                    for c in block[1:]:
                        cleaned_lines.append(c)
                    cleaned_lines.append("")  # 添加空行
                    index_counter += 1
                    block = []  # 清空block

                # 修正vtt->srt时间戳
                line = line.replace('.', ',')  # 00:00:01.000 -> 00:00:01,000
                # 移除多余的 align: start position:0% 之类
                line = re.sub(r'\s*align:\w+|\s*position:\d+%', '', line).strip()
                block.append(line)
            else:
                # 非时间戳行(字幕内容)
                text = re.sub(r'<[^>]+>', '', line)  # 去掉HTML标签
                if text and text != last_line_text:
                    # 处理长行
                    splitted = split_long_lines_to_time_blocks(text, max_length=max_length)
                    block.extend(splitted)
                    last_line_text = text

        # 处理最后一个block
        if block:
            cleaned_lines.append(str(index_counter))
            cleaned_lines.append(block[0])  # 时间戳
            for c in block[1:]:
                cleaned_lines.append(c)
            cleaned_lines.append("")  # 添加空行

        # 写到 srt
        with open(srt_file_path, 'w', encoding='utf-8') as sf:
            for cl in cleaned_lines:
                sf.write(cl + '\n')

        logger.warning(f"已将 {vtt_file_path} 转成 {srt_file_path}")
    except Exception as e:
        logger.error(f"convert_vtt_to_srt error: {e}")

def split_long_lines_to_time_blocks(text, max_length=50):
    """
    将长字幕拆分为多个时间块，每个块的字符数不超过 max_length。
    如果一行字幕超过最大长度，则将其拆分成多个部分，每个部分的字符数不超过 max_length。

    参数:
    - text: 长字幕文本
    - max_length: 每个时间块的最大字符数

    返回:
    - 返回字幕拆分后的多个部分，每个部分不超过 max_length 字符。
    """
    split_texts = []
    while len(text) > max_length:
        split_texts.append(text[:max_length])  # 分割字幕文本
        text = text[max_length:]  # 更新剩余文本
    split_texts.append(text)  # 添加剩余部分
    return split_texts

def convert_srt_to_txt(srt_file_path, txt_file_path):
    """
    将 .srt 文件转换为纯文本，保留字幕内容，去除时间戳和其他无用信息。
    """
    try:
        if not os.path.exists(srt_file_path):
            logger.error(f"转换失败，未找到 .srt 文件: {srt_file_path}")
            return

        with open(srt_file_path, 'r', encoding='utf-8') as srt_file:
            lines = srt_file.readlines()

        cleaned_lines = []
        for line in lines:
            # 跳过字幕序号和时间戳，只保留字幕文本
            if line.strip().isdigit() or '-->' in line:
                continue
            clean_line = re.sub(r"<[^>]+>", "", line).strip()  # 删除 HTML 标签
            if clean_line:  # 跳过空行
                cleaned_lines.append(clean_line)

        with open(txt_file_path, 'w', encoding='utf-8') as txt_file:
            txt_file.write(' '.join(cleaned_lines))

        logger.warning(f"已将 {srt_file_path} 转换为纯文字: {txt_file_path}")
    except Exception as e:
        logger.error(f"转换 .srt 为 .txt 时出错: {e}")

def check_chinese_subtitle_content(subtitle_file_path, min_chinese_chars=100):
    """
    检查字幕文件中是否包含足够数量的中文字符

    参数:
    - subtitle_file_path: 字幕文件路径
    - min_chinese_chars: 最少需要的中文字符数量

    返回:
    - bool: 如果中文字符数量符合要求返回True，否则返回False
    """
    try:
        if not os.path.exists(subtitle_file_path):
            logger.warning(f"字幕文件不存在: {subtitle_file_path}")
            return False

        # 读取字幕文件内容
        with open(subtitle_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 使用正则表达式匹配所有中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', content)
        chinese_count = len(chinese_chars)

        logger.warning(f"字幕文件 {subtitle_file_path} 中包含 {chinese_count} 个中文字符")

        if chinese_count >= min_chinese_chars:
            return True
        else:
            logger.warning(f"字幕文件中的中文字符数量不足: {chinese_count}/{min_chinese_chars}")
            return False
    except Exception as e:
        logger.error(f"检查字幕中文内容时出错: {e}")
        return False
