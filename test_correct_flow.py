#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的流程：VTT转SRT不限制字数，翻译后再检查41字符限制
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def convert_vtt_to_srt_no_limit(vtt_file_path, srt_file_path):
    """
    VTT转SRT，不做字数限制，保持完整句子
    """
    print(f"VTT转SRT (不限制字数): {vtt_file_path} -> {srt_file_path}")
    
    try:
        with open(vtt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        cleaned_lines = []
        block = []
        index_counter = 1
        last_line_text = None

        for line in lines:
            line = line.strip()

            # 跳过头部信息
            if (not line or 
                line.startswith("WEBVTT") or 
                line.startswith("Kind:") or 
                line.startswith("Language:")):
                continue

            # 如果这是时间戳行
            if '-->' in line:
                # 先把上一个block写入
                if block and len(block) > 1:
                    content_lines = [content.strip() for content in block[1:] if content.strip()]
                    if content_lines:
                        cleaned_lines.append(str(index_counter))
                        cleaned_lines.append(block[0])  # 时间戳
                        for content in content_lines:
                            cleaned_lines.append(content)
                        cleaned_lines.append("")
                        index_counter += 1
                    
                block = []

                # 修正vtt->srt时间戳
                line = line.replace('.', ',')
                line = re.sub(r'\s*align:\w+|\s*position:\d+%', '', line).strip()
                block.append(line)
                
            else:
                # 字幕内容行
                text = re.sub(r'<[^>]+>', '', line)  # 去掉HTML标签
                text = re.sub(r'<\d+:\d+:\d+\.\d+>', '', text)  # 去掉时间标记
                text = text.strip()
                
                if text and text != last_line_text:
                    # 不做字数限制，保持完整内容
                    block.append(text)
                    last_line_text = text

        # 处理最后一个block
        if block and len(block) > 1:
            content_lines = [content.strip() for content in block[1:] if content.strip()]
            if content_lines:
                cleaned_lines.append(str(index_counter))
                cleaned_lines.append(block[0])
                for content in content_lines:
                    cleaned_lines.append(content)
                cleaned_lines.append("")

        # 写入SRT文件
        with open(srt_file_path, 'w', encoding='utf-8') as sf:
            for cl in cleaned_lines:
                sf.write(cl + '\n')

        print(f"✅ VTT转SRT完成，生成 {index_counter - 1} 个字幕块")
        return True
        
    except Exception as e:
        print(f"❌ VTT转SRT出错: {e}")
        return False

def check_file_has_punctuation(srt_content):
    """检查整个SRT文件是否包含标点符号"""
    print("检查文件标点符号...")
    
    # 提取所有字幕内容
    subtitle_content = ""
    lines = srt_content.split('\n')
    
    for line in lines:
        line = line.strip()
        if (line.isdigit() or '-->' in line or not line):
            continue
        subtitle_content += line + " "
    
    # 检查标点符号
    punctuation_matches = re.findall(r'[.!?,:;]', subtitle_content)
    
    print(f"   字幕内容长度: {len(subtitle_content)} 字符")
    print(f"   标点符号数量: {len(punctuation_matches)}")
    
    if len(punctuation_matches) == 0:
        print("❌ 没有标点符号，字幕质量有问题")
        return False
    else:
        print(f"✅ 有 {len(punctuation_matches)} 个标点符号，可以翻译")
        return True

def extract_complete_sentences(srt_content):
    """提取完整句子"""
    print("提取完整句子...")
    
    # 提取所有字幕内容
    subtitle_lines = []
    lines = srt_content.split('\n')
    
    for line in lines:
        line = line.strip()
        if (line.isdigit() or '-->' in line or not line):
            continue
        subtitle_lines.append(line)
    
    # 合并所有内容
    full_text = ' '.join(subtitle_lines)
    print(f"   合并文本长度: {len(full_text)} 字符")
    print(f"   前100字符: '{full_text[:100]}...'")
    
    # 按句号分割句子
    sentences = re.split(r'(?<=[.!?])\s+', full_text.strip())
    sentences = [s.strip() for s in sentences if s.strip()]
    
    print(f"   分割出 {len(sentences)} 个句子")
    return sentences

def split_long_text(text, max_length=41):
    """翻译后检查41字符限制，超过才拆分"""
    if len(text) <= max_length:
        return [text]
    
    parts = []
    while len(text) > max_length:
        split_pos = max_length
        
        # 向前查找合适的断点
        for i in range(max_length - 1, max(0, max_length - 10), -1):
            if text[i] in '，。！？；：、 ':
                split_pos = i + 1
                break
        
        parts.append(text[:split_pos].strip())
        text = text[split_pos:].strip()
    
    if text:
        parts.append(text)
    
    return parts

def translate_sentences_with_41_limit(sentences, max_test=5):
    """翻译句子，翻译后检查41字符限制"""
    print(f"翻译句子 (测试前{max_test}个)...")
    
    results = []
    
    for i, sentence in enumerate(sentences[:max_test], 1):
        print(f"\n句子 {i}: '{sentence}'")
        print(f"   原文长度: {len(sentence)} 字符")
        
        try:
            # 翻译
            if sentence in ['[Music]', '[Applause]', '[Laughter]']:
                translation_map = {
                    '[Music]': '[音乐]',
                    '[Applause]': '[掌声]',
                    '[Laughter]': '[笑声]'
                }
                translated = translation_map.get(sentence, sentence)
                print(f"   特殊标记: '{translated}'")
            else:
                translated = translator.translate(sentence)
                print(f"   翻译结果: '{translated}'")
                time.sleep(0.3)
            
            # 翻译后检查41字符限制
            print(f"   译文长度: {len(translated)} 字符")
            if len(translated) > 41:
                print(f"   ⚠️  超过41字符限制，需要拆分")
                split_parts = split_long_text(translated, max_length=41)
                print(f"   拆分为 {len(split_parts)} 部分:")
                for j, part in enumerate(split_parts, 1):
                    print(f"     部分{j}: '{part}' ({len(part)} 字符)")
                
                results.append({
                    'original': sentence,
                    'translated': translated,
                    'split_parts': split_parts,
                    'needs_split': True
                })
            else:
                print(f"   ✅ 符合41字符限制")
                results.append({
                    'original': sentence,
                    'translated': translated,
                    'split_parts': [translated],
                    'needs_split': False
                })
                
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
            results.append({
                'original': sentence,
                'translated': sentence,
                'split_parts': [sentence],
                'needs_split': False,
                'error': str(e)
            })
    
    return results

def test_correct_flow():
    """测试正确的流程"""
    print("正确流程测试：VTT转SRT不限字数 → 翻译 → 41字符检查")
    print("=" * 60)
    
    vtt_file = "complete_english.en.vtt"
    srt_file = "complete_english.en.srt"
    chinese_srt_file = "complete_english.zh-Hans.srt"
    
    if not os.path.exists(vtt_file):
        print(f"❌ VTT文件不存在: {vtt_file}")
        return False
    
    print(f"✅ 找到VTT文件: {vtt_file}")
    
    # 步骤1: VTT转SRT (不限制字数)
    if not convert_vtt_to_srt_no_limit(vtt_file, srt_file):
        return False
    
    # 步骤2: 读取SRT文件
    with open(srt_file, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    # 步骤3: 检查标点符号
    if not check_file_has_punctuation(srt_content):
        print("字幕质量检查失败")
        return False
    
    # 步骤4: 提取完整句子
    sentences = extract_complete_sentences(srt_content)
    
    # 显示前几个句子
    print(f"\n前5个完整句子:")
    for i, sentence in enumerate(sentences[:5], 1):
        print(f"   {i}. '{sentence}'")
    
    # 步骤5: 翻译句子并检查41字符限制
    results = translate_sentences_with_41_limit(sentences, max_test=5)
    
    # 步骤6: 生成简单的中文SRT
    print(f"\n生成中文SRT文件...")
    chinese_lines = []
    index = 1
    
    for result in results:
        for part in result['split_parts']:
            chinese_lines.append(str(index))
            chinese_lines.append("00:00:00,000 --> 00:00:03,000")  # 简化时间戳
            chinese_lines.append(part)
            chinese_lines.append("")
            index += 1
    
    with open(chinese_srt_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(chinese_lines))
    
    print(f"✅ 正确流程测试完成")
    print(f"   英文SRT: {srt_file}")
    print(f"   中文SRT: {chinese_srt_file}")
    
    # 总结
    success_count = sum(1 for r in results if 'error' not in r)
    split_count = sum(1 for r in results if r['needs_split'])
    
    print(f"\n翻译总结:")
    print(f"   成功翻译: {success_count}/{len(results)}")
    print(f"   需要拆分: {split_count}")
    
    return True

if __name__ == "__main__":
    test_correct_flow()
