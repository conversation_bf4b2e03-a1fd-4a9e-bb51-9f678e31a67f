#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Gemini API的输入字符限制
"""

import os
import re
import time
import requests
import json

class APILimitTester:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
    
    def test_input_limit(self, text, description):
        """测试特定长度的输入"""
        print(f"\n测试: {description}")
        print(f"输入长度: {len(text)} 字符")
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": text
                }]
            }],
            "generationConfig": {
                "temperature": 0.3,
                "maxOutputTokens": 1024,
            }
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers=headers,
                data=json.dumps(payload),
                timeout=60
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text']
                    print(f"✅ 成功! 耗时: {end_time - start_time:.2f}秒")
                    print(f"   返回长度: {len(content)} 字符")
                    print(f"   返回内容: '{content[:100]}...'")
                    return True
                else:
                    print(f"❌ API返回格式错误")
                    return False
            else:
                print(f"❌ API错误: {response.status_code}")
                print(f"   错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False

def test_gemini_limits():
    """测试Gemini API的各种限制"""
    print("Gemini API限制测试")
    print("=" * 50)
    
    api_key = "AIzaSyBrEmZW9wqmyil7xPsbK2fgMb7LjII8NlQ"
    tester = APILimitTester(api_key)
    
    # 读取我们的句子
    srt_file = "complete_english.en.srt"
    
    if not os.path.exists(srt_file):
        print(f"❌ 文件不存在: {srt_file}")
        return False
    
    # 提取句子
    with open(srt_file, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    subtitle_lines = []
    lines = srt_content.split('\n')
    
    for line in lines:
        line = line.strip()
        if (line.isdigit() or '-->' in line or not line):
            continue
        subtitle_lines.append(line)
    
    full_text = ' '.join(subtitle_lines)
    sentences = re.split(r'(?<=[.!?])\s+', full_text.strip())
    sentences = [s.strip() for s in sentences if s.strip()]
    
    print(f"总共 {len(sentences)} 个句子")
    
    # 测试不同数量的句子
    test_cases = [
        (5, "5个句子"),
        (10, "10个句子"), 
        (15, "15个句子"),
        (20, "20个句子"),
        (30, "30个句子"),
        (45, "全部45个句子")
    ]
    
    for count, description in test_cases:
        if count > len(sentences):
            count = len(sentences)
            description = f"全部{count}个句子"
        
        # 构建测试文本
        test_sentences = sentences[:count]
        sentences_text = ""
        for i, sentence in enumerate(test_sentences, 1):
            sentences_text += f"{i}. {sentence}\n"
        
        prompt = f"""请将以下{count}个英文句子翻译成中文：

{sentences_text}

请按序号返回翻译结果。"""
        
        # 测试这个长度
        success = tester.test_input_limit(prompt, description)
        
        if not success:
            print(f"⚠️  在 {count} 个句子时达到限制")
            break
        
        time.sleep(1)  # 避免API限制
    
    # 测试纯字符长度限制
    print(f"\n" + "=" * 50)
    print("测试纯字符长度限制")
    print("=" * 50)
    
    # 生成不同长度的测试文本
    base_text = "Please translate this English text to Chinese: "
    test_lengths = [1000, 2000, 3000, 5000, 8000, 10000, 15000, 20000]
    
    for length in test_lengths:
        # 生成指定长度的文本
        repeat_count = (length - len(base_text)) // 50
        test_text = base_text + ("This is a test sentence for API limit testing. " * repeat_count)
        test_text = test_text[:length]  # 精确截取到指定长度
        
        success = tester.test_input_limit(test_text, f"{length} 字符")
        
        if not success:
            print(f"⚠️  在 {length} 字符时达到限制")
            break
        
        time.sleep(1)
    
    print(f"\n🎉 API限制测试完成")

if __name__ == "__main__":
    test_gemini_limits()
