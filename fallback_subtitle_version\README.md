# YouTube视频下载系统 - 字幕降级策略版本

这是YouTube视频下载系统的字幕降级策略版本，专门解决中文字幕下载遇到429错误的问题。

## 🎯 核心特性

### 智能降级策略
当遇到中文字幕下载429错误时，自动切换到英文字幕+谷歌翻译的备用方案：

1. **第一阶段：尝试中文字幕**
   - 下载 `zh-Hans` 中文字幕
   - 遇到429错误时重试3次
   - 每次重试间隔递增（10秒、20秒、30秒）

2. **第二阶段：降级策略**
   - 下载英文字幕 (`en`)
   - 转换VTT为SRT格式
   - 使用谷歌翻译将英文SRT翻译为中文
   - 保存为标准的 `.zh-Hans.srt` 格式

### 完整的处理流程
```
字幕处理 → 封面下载 → 时间戳提取 → 视频下载
```

## 📁 文件结构
```
fallback_subtitle_version/
├── main_fallback.py           # 主程序入口
├── config.py                  # 配置文件
├── subtitle_fallback.py       # 字幕降级策略核心模块
├── downloader_fallback.py     # 下载器（集成降级策略）
├── monitor_fallback.py        # 监控模块
├── utils.py                   # 工具函数
├── translation.py             # 标题翻译
├── video_info.py              # 视频信息获取
├── thumbnail_downloader.py    # 缩略图下载
├── test_fallback.py           # 测试脚本
├── channels_videos_test.txt   # 频道配置
├── keywords.txt               # 过滤关键词
└── README.md                  # 说明文档
```

## 🚀 使用方法

### 1. 安装依赖
```bash
pip install feedparser httpx googletrans==4.0.0-rc1 deep-translator fuzzywuzzy requests yt-dlp
```

### 2. 配置设置
编辑 `config.py` 中的路径配置：
```python
VTT_HAN_FOLDER = "D:/ytb_python_download/vtt_han"
SRT_HAN_FOLDER = "D:/ytb_python_download/srt_han"
DOWNLOAD_PATHS = {
    "required": "D:/ytb_python_download/",
    "alternative": "D:/ytb_python_download/alternative"
}
```

### 3. 运行测试
```bash
python test_fallback.py
```

### 4. 启动程序
```bash
python main_fallback.py
```

## 🔧 降级策略详解

### 策略触发条件
- 中文字幕下载返回HTTP 429错误
- 重试3次后仍然失败
- 自动切换到英文字幕+翻译方案

### 翻译质量保证
- 使用Google Translate进行翻译
- 保留SRT时间戳格式
- 自动处理HTML标签清理
- 支持长字幕行拆分

### 文件输出格式
无论使用哪种策略，最终都输出标准格式：
- `【频道名】视频标题.zh-Hans.vtt` - VTT格式（如果是中文字幕）
- `【频道名】视频标题.zh-Hans.srt` - SRT格式
- `【频道名】视频标题.zh-Hans.txt` - 纯文本格式

## 📊 优势对比

| 特性 | 原版 | 降级策略版本 |
|------|------|-------------|
| 中文字幕下载 | ✅ | ✅ |
| 429错误处理 | ❌ 直接失败 | ✅ 自动降级 |
| 英文字幕翻译 | ❌ | ✅ |
| 成功率 | 低（遇到429就失败） | 高（双重保障） |
| 字幕质量 | 高（原生中文） | 中-高（翻译质量） |
| 稳定性 | 低 | 高 |

## 🔍 工作原理

### 1. 中文字幕尝试
```python
# 尝试下载中文字幕
success, error_type = download_chinese_subtitle(video_url, channel_name, title)
if error_type == "429_ERROR":
    # 检测到429错误，准备降级
```

### 2. 降级策略执行
```python
# 下载英文字幕
download_english_subtitle() 
# 转换格式
convert_vtt_to_srt()
# 翻译内容
translate_srt_file()
# 保存为中文格式
save_as_chinese_subtitle()
```

### 3. 质量验证
```python
# 检查中文字符数量
check_chinese_subtitle_content(subtitle_path, min_chinese_chars=50)
```

## ⚙️ 配置选项

### 重试设置
```python
retries = 3  # 中文字幕重试次数
delay = 10   # 重试间隔（秒）
```

### 翻译设置
```python
translator = GoogleTranslator(source='en', target='zh-CN')
time.sleep(0.2)  # 翻译间隔，避免API限制
```

### 质量检查
```python
min_chinese_chars = 50  # 最少中文字符数
min_size_kb = 1        # 最小文件大小
```

## 🐛 故障排除

### 常见问题

1. **谷歌翻译失败**
   ```
   解决：检查网络连接，可能需要代理
   ```

2. **字幕文件为空**
   ```
   解决：检查视频是否有英文字幕可用
   ```

3. **翻译质量差**
   ```
   解决：这是自动翻译的限制，可调整质量检查参数
   ```

### 日志分析
查看 `script.log` 文件了解详细执行过程：
```
第1步: 尝试下载中文字幕...
检测到429错误 - 请求过多
启动降级策略：下载英文字幕并翻译为中文
步骤1: 下载英文字幕...
步骤2: 转换英文VTT为SRT...
步骤3: 翻译英文SRT为中文SRT...
降级策略成功：英文字幕翻译完成
```

## 🎉 总结

这个降级策略版本完美解决了中文字幕429错误的问题，通过智能的备用方案确保字幕下载的高成功率，同时保持与原版相同的文件格式和质量标准。

**推荐使用场景：**
- 经常遇到中文字幕429错误
- 需要高成功率的字幕下载
- 可以接受翻译质量的轻微损失
- 希望保持原有的工作流程
