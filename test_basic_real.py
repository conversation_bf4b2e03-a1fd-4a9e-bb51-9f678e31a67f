#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最基本的真实字幕测试
"""

import os
import re

def test_basic_real():
    """最基本的真实字幕测试"""
    print("最基本的真实字幕测试")
    print("=" * 30)
    
    english_srt_file = "real_test_improved.en.srt"
    
    if not os.path.exists(english_srt_file):
        print(f"❌ 文件不存在: {english_srt_file}")
        return False
    
    print(f"✅ 找到文件: {english_srt_file}")
    print(f"   文件大小: {os.path.getsize(english_srt_file)} 字节")
    
    # 读取文件
    try:
        with open(english_srt_file, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"✅ 文件读取成功，内容长度: {len(content)} 字符")
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return False
    
    # 显示前几行
    lines = content.split('\n')
    print(f"\n文件总行数: {len(lines)}")
    print("前10行:")
    for i, line in enumerate(lines[:10], 1):
        print(f"   {i:2d}: '{line}'")
    
    # 提取字幕内容
    subtitle_lines = []
    for line in lines:
        line = line.strip()
        # 跳过序号、时间戳和空行
        if (line.isdigit() or 
            '-->' in line or 
            not line):
            continue
        subtitle_lines.append(line)
    
    print(f"\n提取出 {len(subtitle_lines)} 行字幕内容")
    print("前10行字幕内容:")
    for i, line in enumerate(subtitle_lines[:10], 1):
        print(f"   {i:2d}: '{line}'")
    
    # 合并所有字幕内容
    full_text = ' '.join(subtitle_lines)
    print(f"\n合并后的文本长度: {len(full_text)} 字符")
    print(f"前100字符: '{full_text[:100]}{'...' if len(full_text) > 100 else ''}'")
    
    # 检查标点符号
    punctuation_matches = re.findall(r'[.!?,:;]', full_text)
    print(f"\n标点符号检查:")
    print(f"   找到 {len(punctuation_matches)} 个标点符号")
    if len(punctuation_matches) > 0:
        print(f"   前10个: {punctuation_matches[:10]}")
        print("✅ 有标点符号，字幕质量OK")
    else:
        print("❌ 没有标点符号，字幕质量有问题")
        return False
    
    # 尝试分割句子
    sentences = re.split(r'(?<=[.!?])\s+', full_text.strip())
    sentences = [s.strip() for s in sentences if s.strip()]
    
    print(f"\n句子分割:")
    print(f"   分割出 {len(sentences)} 个句子")
    print("前5个句子:")
    for i, sentence in enumerate(sentences[:5], 1):
        print(f"   {i}. '{sentence[:50]}{'...' if len(sentence) > 50 else ''}'")
    
    print(f"\n✅ 基本测试完成")
    return True

if __name__ == "__main__":
    test_basic_real()
