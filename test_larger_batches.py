#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更大的批量大小 - 15句、20句、30句甚至全部一次性翻译
"""

import os
import re
import time
import requests
import json

class LargeBatchGeminiTranslator:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
    
    def large_batch_translate(self, sentences):
        """大批量翻译多个句子"""
        # 构建批量翻译的prompt
        sentences_text = ""
        for i, sentence in enumerate(sentences, 1):
            sentences_text += f"{i}. {sentence}\n"
        
        prompt = f"""请将以下{len(sentences)}个英文句子翻译成中文，要求：
1. 翻译要自然流畅，保持原文语气
2. 如果是歌词，保持韵律感
3. 严格按照序号返回翻译结果
4. 格式必须是：序号. 中文翻译
5. 不要添加任何其他解释或说明

英文原文：
{sentences_text}

中文翻译："""

        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.2,
                "topK": 1,
                "topP": 1,
                "maxOutputTokens": 4096,  # 增加输出长度限制
            }
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers=headers,
                data=json.dumps(payload),
                timeout=120  # 增加超时时间
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text']
                    return self.parse_batch_result(content, len(sentences))
                else:
                    raise Exception("Gemini API返回格式错误")
            else:
                raise Exception(f"Gemini API错误: {response.status_code}")
                
        except Exception as e:
            raise Exception(f"Gemini大批量翻译失败: {str(e)}")
    
    def parse_batch_result(self, content, expected_count):
        """解析批量翻译结果"""
        content = content.strip()
        print(f"API返回内容前500字符: {content[:500]}...")
        
        # 按行分割
        lines = content.split('\n')
        translations = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 匹配格式：数字. 翻译内容
            match = re.match(r'^(\d+)\.\s*(.+)$', line)
            if match:
                index = int(match.group(1))
                translation = match.group(2).strip()
                translations.append((index, translation))
        
        print(f"解析出 {len(translations)} 个翻译结果")
        
        # 按序号排序并提取翻译内容
        translations.sort(key=lambda x: x[0])
        result = [t[1] for t in translations]
        
        # 如果数量不匹配，显示详细信息
        if len(result) != expected_count:
            print(f"⚠️ 翻译结果数量不匹配: 期望{expected_count}个，实际{len(result)}个")
            print("解析出的翻译:")
            for i, (idx, trans) in enumerate(translations):
                print(f"  {idx}. {trans}")
            
            # 如果数量接近，尝试补齐
            if len(result) >= expected_count * 0.8:  # 至少80%成功
                print("尝试使用部分结果...")
                return result[:expected_count] if len(result) > expected_count else result
            else:
                raise Exception(f"翻译结果数量差异过大")
        
        return result

def test_large_batches():
    """测试大批量翻译"""
    print("大批量翻译测试")
    print("=" * 60)
    
    # 使用Gemini API密钥
    gemini_api_key = "AIzaSyBrEmZW9wqmyil7xPsbK2fgMb7LjII8NlQ"
    
    # 初始化翻译器
    translator = LargeBatchGeminiTranslator(gemini_api_key)
    
    # 读取英文SRT文件
    srt_file = "complete_english.en.srt"
    
    if not os.path.exists(srt_file):
        print(f"❌ SRT文件不存在: {srt_file}")
        return False
    
    # 提取句子
    with open(srt_file, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    # 提取所有字幕内容
    subtitle_lines = []
    lines = srt_content.split('\n')
    
    for line in lines:
        line = line.strip()
        if (line.isdigit() or '-->' in line or not line):
            continue
        subtitle_lines.append(line)
    
    # 合并所有内容
    full_text = ' '.join(subtitle_lines)
    
    # 按句号分割句子
    sentences = re.split(r'(?<=[.!?])\s+', full_text.strip())
    sentences = [s.strip() for s in sentences if s.strip()]
    
    print(f"总共 {len(sentences)} 个句子")
    
    # 测试不同的大批量大小
    batch_sizes = [15, 20, 30, 45]  # 最后一个是全部句子
    
    for batch_size in batch_sizes:
        print(f"\n" + "=" * 60)
        print(f"测试批量大小: {batch_size} 句")
        print("=" * 60)
        
        # 如果批量大小超过总句子数，使用全部句子
        test_sentences = sentences[:min(batch_size, len(sentences))]
        actual_count = len(test_sentences)
        
        print(f"实际翻译句子数: {actual_count}")
        
        # 显示前几个句子
        print("前5个句子:")
        for i, sentence in enumerate(test_sentences[:5], 1):
            print(f"   {i}. '{sentence[:50]}{'...' if len(sentence) > 50 else ''}'")
        
        if actual_count > 5:
            print(f"   ... 还有 {actual_count - 5} 个句子")
        
        try:
            start_time = time.time()
            
            # 大批量翻译
            print(f"\n开始翻译 {actual_count} 个句子...")
            translations = translator.large_batch_translate(test_sentences)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            print(f"\n✅ 翻译成功!")
            print(f"总耗时: {total_time:.2f}秒")
            print(f"平均每句: {total_time / actual_count:.3f}秒")
            print(f"翻译速度: {actual_count / total_time:.2f} 句/秒")
            
            # 检查翻译质量
            long_translations = [t for t in translations if len(t) > 41]
            print(f"超过41字符的翻译: {len(long_translations)}")
            
            # 显示前几个翻译结果
            print(f"\n前5个翻译结果:")
            for i, (original, translated) in enumerate(zip(test_sentences[:5], translations[:5]), 1):
                print(f"   {i}. '{original[:40]}...' → '{translated}' ({len(translated)}字符)")
            
            # 保存结果
            output_file = f"large_batch_test_{actual_count}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"大批量翻译测试 - {actual_count}句\n")
                f.write(f"总耗时: {total_time:.2f}秒\n")
                f.write(f"翻译速度: {actual_count / total_time:.2f} 句/秒\n\n")
                
                for i, (original, translated) in enumerate(zip(test_sentences, translations), 1):
                    f.write(f"{i}. {original}\n")
                    f.write(f"   → {translated} ({len(translated)}字符)\n\n")
            
            print(f"结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"❌ 批量翻译失败: {e}")
            
        # 避免API限制
        time.sleep(2)
    
    print(f"\n🎉 大批量翻译测试完成！")
    return True

if __name__ == "__main__":
    test_large_batches()
