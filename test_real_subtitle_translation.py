#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实字幕文件的完整翻译流程
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def check_file_has_punctuation(srt_content):
    """检查整个SRT文件是否包含标点符号"""
    print("=== 检查整个文件是否包含标点符号 ===")
    
    # 提取所有字幕内容（排除序号和时间戳）
    subtitle_content = ""
    lines = srt_content.split('\n')
    
    for line in lines:
        line = line.strip()
        # 跳过序号、时间戳和空行
        if (line.isdigit() or 
            '-->' in line or 
            not line):
            continue
        subtitle_content += line + " "
    
    print(f"提取的字幕内容前200字符: '{subtitle_content[:200]}{'...' if len(subtitle_content) > 200 else ''}'")
    print(f"总字符数: {len(subtitle_content)}")
    
    # 检查是否包含标点符号
    punctuation_pattern = r'[.!?,:;]'
    punctuation_matches = re.findall(punctuation_pattern, subtitle_content)
    
    print(f"找到的标点符号: {punctuation_matches[:20]}{'...' if len(punctuation_matches) > 20 else ''}")
    print(f"标点符号数量: {len(punctuation_matches)}")
    
    if len(punctuation_matches) == 0:
        print("❌ 整个文件没有标点符号，字幕质量有问题")
        return False
    else:
        print(f"✅ 文件包含 {len(punctuation_matches)} 个标点符号，可以进行翻译")
        return True

def parse_srt_blocks(srt_content):
    """解析SRT文件为字幕块"""
    blocks = []
    srt_blocks = srt_content.strip().split('\n\n')
    
    for block in srt_blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            try:
                index = int(lines[0])
                timestamp = lines[1]
                content_lines = lines[2:]
                content = ' '.join(content_lines).strip()
                if content:
                    blocks.append({
                        'index': index,
                        'timestamp': timestamp,
                        'content': content
                    })
            except ValueError:
                continue  # 跳过格式错误的块
    
    return blocks

def extract_sentences_from_blocks(blocks):
    """从字幕块中提取完整句子"""
    print("\n=== 从字幕块中提取句子 ===")
    
    # 合并所有字幕内容
    full_text = ""
    for block in blocks:
        full_text += block['content'] + " "
    
    print(f"合并的完整文本前300字符: '{full_text[:300]}{'...' if len(full_text) > 300 else ''}'")
    
    # 按句号分割句子
    sentences = re.split(r'(?<=[.!?])\s+', full_text.strip())
    sentences = [s.strip() for s in sentences if s.strip()]
    
    print(f"提取出 {len(sentences)} 个句子:")
    for i, sentence in enumerate(sentences[:10], 1):  # 只显示前10个
        print(f"  {i}. '{sentence[:80]}{'...' if len(sentence) > 80 else ''}'")
    
    if len(sentences) > 10:
        print(f"  ... 还有 {len(sentences) - 10} 个句子")
    
    return sentences

def split_long_text(text, max_length=41):
    """将超长文本按41字符限制拆分"""
    if len(text) <= max_length:
        return [text]
    
    parts = []
    while len(text) > max_length:
        split_pos = max_length
        
        # 向前查找合适的断点
        for i in range(max_length - 1, max(0, max_length - 10), -1):
            if text[i] in '，。！？；：、 ':
                split_pos = i + 1
                break
        
        parts.append(text[:split_pos].strip())
        text = text[split_pos:].strip()
    
    if text:
        parts.append(text)
    
    return parts

def translate_sentences(sentences, max_sentences=10):
    """翻译句子（限制数量用于测试）"""
    print(f"\n=== 开始翻译前{max_sentences}个句子 ===")
    
    translated_results = []
    
    for i, sentence in enumerate(sentences[:max_sentences], 1):
        print(f"\n句子 {i}: '{sentence}'")
        print(f"   原文长度: {len(sentence)} 字符")
        
        try:
            # 特殊标记处理
            if sentence in ['[Music]', '[Applause]', '[Laughter]']:
                translation_map = {
                    '[Music]': '[音乐]',
                    '[Applause]': '[掌声]',
                    '[Laughter]': '[笑声]'
                }
                translated = translation_map.get(sentence, sentence)
                print(f"   特殊标记翻译: '{translated}'")
            else:
                # 翻译句子
                translated = translator.translate(sentence)
                print(f"   翻译结果: '{translated}'")
                time.sleep(0.3)  # 避免API限制
            
            # 检查41字符限制
            print(f"   翻译长度: {len(translated)} 字符")
            if len(translated) > 41:
                print(f"   ⚠️  超过41字符限制，进行拆分")
                split_parts = split_long_text(translated, max_length=41)
                print(f"   拆分为 {len(split_parts)} 部分:")
                for j, part in enumerate(split_parts, 1):
                    print(f"     部分{j}: '{part}' ({len(part)} 字符)")
                translated_results.append({
                    'original': sentence,
                    'translated': translated,
                    'split_parts': split_parts,
                    'needs_split': True
                })
            else:
                print(f"   ✅ 符合41字符限制")
                translated_results.append({
                    'original': sentence,
                    'translated': translated,
                    'split_parts': [translated],
                    'needs_split': False
                })
                
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
            translated_results.append({
                'original': sentence,
                'translated': sentence,  # 保持原文
                'split_parts': [sentence],
                'needs_split': False,
                'error': str(e)
            })
    
    return translated_results

def generate_final_srt(translated_results, original_blocks):
    """生成最终的中文SRT文件"""
    print(f"\n=== 生成最终SRT文件 ===")
    
    srt_lines = []
    index_counter = 1
    
    # 简化处理：将翻译结果按顺序分配到原有时间戳
    block_index = 0
    
    for result in translated_results:
        for part in result['split_parts']:
            if block_index < len(original_blocks):
                block = original_blocks[block_index]
                srt_lines.append(str(index_counter))
                srt_lines.append(block['timestamp'])
                srt_lines.append(part)
                srt_lines.append('')
                
                print(f"   块 {index_counter}: [{block['timestamp']}] '{part}'")
                
                index_counter += 1
                block_index += 1
    
    return '\n'.join(srt_lines)

def test_real_subtitle_translation():
    """测试真实字幕文件的完整翻译流程"""
    print("真实字幕文件完整翻译测试")
    print("=" * 60)
    
    # 使用之前下载的真实英文SRT文件
    english_srt_file = "real_test_improved.en.srt"
    chinese_srt_file = "real_test_final_translation.zh-Hans.srt"
    
    if not os.path.exists(english_srt_file):
        print(f"❌ 真实英文SRT文件不存在: {english_srt_file}")
        print("请先运行之前的测试生成英文SRT文件")
        return False
    
    print(f"✅ 找到真实英文SRT文件: {english_srt_file}")
    print(f"   文件大小: {os.path.getsize(english_srt_file)} 字节")
    
    # 步骤1: 读取文件
    with open(english_srt_file, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    print(f"   内容长度: {len(srt_content)} 字符")
    
    # 步骤2: 检查整个文件是否有标点符号
    if not check_file_has_punctuation(srt_content):
        print("\n🚫 字幕文件质量检查失败：没有标点符号")
        return False
    
    # 步骤3: 解析字幕块
    blocks = parse_srt_blocks(srt_content)
    print(f"\n解析出 {len(blocks)} 个有效字幕块")
    
    # 显示前几个块
    print("前5个字幕块:")
    for i, block in enumerate(blocks[:5], 1):
        print(f"  {i}. [{block['index']}] {block['timestamp']} '{block['content']}'")
    
    # 步骤4: 提取句子
    sentences = extract_sentences_from_blocks(blocks)
    
    # 步骤5: 翻译句子（只翻译前10个进行测试）
    translated_results = translate_sentences(sentences, max_sentences=10)
    
    # 步骤6: 生成最终SRT
    final_srt_content = generate_final_srt(translated_results, blocks)
    
    # 步骤7: 保存文件
    with open(chinese_srt_file, 'w', encoding='utf-8') as f:
        f.write(final_srt_content)
    
    print(f"\n✅ 真实字幕翻译测试完成")
    print(f"   输出文件: {chinese_srt_file}")
    print(f"   文件大小: {os.path.getsize(chinese_srt_file)} 字节")
    
    # 步骤8: 显示翻译效果总结
    print(f"\n=== 翻译效果总结 ===")
    success_count = sum(1 for r in translated_results if 'error' not in r)
    error_count = sum(1 for r in translated_results if 'error' in r)
    split_count = sum(1 for r in translated_results if r['needs_split'])
    
    print(f"   翻译成功: {success_count}/{len(translated_results)}")
    print(f"   翻译失败: {error_count}")
    print(f"   需要拆分: {split_count}")
    
    # 显示最终文件的前几行
    print(f"\n最终中文SRT文件前10行:")
    lines = final_srt_content.split('\n')[:10]
    for i, line in enumerate(lines, 1):
        print(f"   {i:2d}: {line}")
    
    return True

if __name__ == "__main__":
    test_real_subtitle_translation()
