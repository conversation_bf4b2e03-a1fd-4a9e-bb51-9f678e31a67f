#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版真实字幕测试
"""

import os
import re
import time
from deep_translator import GoogleTranslator

# 初始化谷歌翻译器
translator = GoogleTranslator(source='en', target='zh-CN')

def test_real_simple():
    """简化版真实字幕测试"""
    print("简化版真实字幕测试")
    print("=" * 40)
    
    english_srt_file = "real_test_improved.en.srt"
    
    if not os.path.exists(english_srt_file):
        print(f"❌ 文件不存在: {english_srt_file}")
        return False
    
    print(f"✅ 找到文件: {english_srt_file}")
    print(f"   文件大小: {os.path.getsize(english_srt_file)} 字节")
    
    # 读取文件
    with open(english_srt_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"   内容长度: {len(content)} 字符")
    
    # 提取字幕内容（排除序号和时间戳）
    subtitle_lines = []
    lines = content.split('\n')
    
    for line in lines:
        line = line.strip()
        # 跳过序号、时间戳和空行
        if (line.isdigit() or 
            '-->' in line or 
            not line):
            continue
        subtitle_lines.append(line)
    
    print(f"   提取出 {len(subtitle_lines)} 行字幕内容")
    
    # 显示前10行
    print("\n前10行字幕内容:")
    for i, line in enumerate(subtitle_lines[:10], 1):
        print(f"   {i:2d}: '{line}'")
    
    # 合并所有内容
    full_text = ' '.join(subtitle_lines)
    print(f"\n合并后的文本前200字符:")
    print(f"   '{full_text[:200]}{'...' if len(full_text) > 200 else ''}'")
    
    # 检查标点符号
    punctuation_matches = re.findall(r'[.!?,:;]', full_text)
    print(f"\n标点符号检查:")
    print(f"   找到 {len(punctuation_matches)} 个标点符号")
    print(f"   前20个: {punctuation_matches[:20]}")
    
    if len(punctuation_matches) == 0:
        print("❌ 没有标点符号，字幕质量有问题")
        return False
    
    print("✅ 有标点符号，可以进行翻译")
    
    # 按句号分割句子
    sentences = re.split(r'(?<=[.!?])\s+', full_text.strip())
    sentences = [s.strip() for s in sentences if s.strip()]
    
    print(f"\n分割出 {len(sentences)} 个句子:")
    for i, sentence in enumerate(sentences[:5], 1):
        print(f"   {i}. '{sentence[:60]}{'...' if len(sentence) > 60 else ''}'")
    
    # 翻译前3个句子进行测试
    print(f"\n开始翻译前3个句子:")
    for i, sentence in enumerate(sentences[:3], 1):
        try:
            print(f"\n句子 {i}: '{sentence}'")
            
            if sentence in ['[Music]', '[Applause]', '[Laughter]']:
                translation_map = {
                    '[Music]': '[音乐]',
                    '[Applause]': '[掌声]',
                    '[Laughter]': '[笑声]'
                }
                translated = translation_map.get(sentence, sentence)
                print(f"   特殊标记: '{translated}'")
            else:
                translated = translator.translate(sentence)
                print(f"   翻译结果: '{translated}'")
                time.sleep(0.5)
            
            # 检查长度
            if len(translated) > 41:
                print(f"   ⚠️  超过41字符 ({len(translated)}字符)")
            else:
                print(f"   ✅ 符合41字符限制 ({len(translated)}字符)")
                
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
    
    print(f"\n✅ 简化版测试完成")
    return True

if __name__ == "__main__":
    test_real_simple()
